<UserControl x:Class="AqlanCenterProApp.Views.Controls.EnhancedChartCard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:lvc="clr-namespace:LiveChartsCore.SkiaSharpView.WPF;assembly=LiveChartsCore.SkiaSharpView.WPF"
             mc:Ignorable="d"
             d:DesignHeight="350"
             d:DesignWidth="500"
             FlowDirection="RightToLeft">

    <Border CornerRadius="12"
            Margin="8"
            Padding="20"
            Background="White"
            Effect="{StaticResource DropShadowEffect}">
        
        <Border.Style>
            <Style TargetType="Border">
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="Black"
                                                  Direction="270"
                                                  ShadowDepth="6"
                                                  BlurRadius="12"
                                                  Opacity="0.25"/>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>
        </Border.Style>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- رأس المخطط -->
            <Grid Grid.Row="0" Margin="0,0,0,15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- العنوان والوصف -->
                <StackPanel Grid.Column="0">
                    <TextBlock Text="{Binding ChartTitle, RelativeSource={RelativeSource AncestorType=UserControl}}"
                               FontSize="18"
                               FontWeight="Bold"
                               Foreground="#2C3E50"
                               Margin="0,0,0,4"/>

                    <TextBlock Text="{Binding ChartSubtitle, RelativeSource={RelativeSource AncestorType=UserControl}}"
                               FontSize="12"
                               Foreground="#7F8C8D"
                               Visibility="{Binding ShowSubtitle, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </StackPanel>

                <!-- أزرار التحكم -->
                <StackPanel Grid.Column="1" 
                            Orientation="Horizontal"
                            VerticalAlignment="Top">

                    <!-- زر التحديث -->
                    <Button Content="🔄"
                            Command="{Binding RefreshCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                            Width="32"
                            Height="32"
                            Margin="4,0,0,0"
                            Background="Transparent"
                            BorderThickness="1"
                            BorderBrush="#E0E0E0"
                            CornerRadius="6"
                            ToolTip="تحديث البيانات">
                        
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#F5F5F5"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- زر الخيارات -->
                    <Button Content="⚙"
                            Command="{Binding SettingsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                            Width="32"
                            Height="32"
                            Margin="4,0,0,0"
                            Background="Transparent"
                            BorderThickness="1"
                            BorderBrush="#E0E0E0"
                            CornerRadius="6"
                            ToolTip="إعدادات المخطط">
                        
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#F5F5F5"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>

            <!-- فلاتر المخطط -->
            <StackPanel Grid.Row="1" 
                        Orientation="Horizontal"
                        Margin="0,0,0,15"
                        Visibility="{Binding ShowFilters, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}">

                <TextBlock Text="الفترة:"
                           FontSize="12"
                           Foreground="#7F8C8D"
                           VerticalAlignment="Center"
                           Margin="0,0,8,0"/>

                <ComboBox ItemsSource="{Binding PeriodOptions, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          SelectedItem="{Binding SelectedPeriod, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          Width="100"
                          Height="28"
                          FontSize="11"
                          Margin="0,0,12,0"/>

                <TextBlock Text="النوع:"
                           FontSize="12"
                           Foreground="#7F8C8D"
                           VerticalAlignment="Center"
                           Margin="0,0,8,0"
                           Visibility="{Binding ShowTypeFilter, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                <ComboBox ItemsSource="{Binding TypeOptions, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          SelectedItem="{Binding SelectedType, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          Width="120"
                          Height="28"
                          FontSize="11"
                          Visibility="{Binding ShowTypeFilter, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StackPanel>

            <!-- منطقة المخطط -->
            <Grid Grid.Row="2">
                <!-- مخطط LiveCharts -->
                <lvc:CartesianChart Series="{Binding ChartSeries, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                    XAxes="{Binding XAxes, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                    YAxes="{Binding YAxes, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                    LegendPosition="{Binding LegendPosition, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                    Visibility="{Binding IsCartesianChart, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                <lvc:PieChart Series="{Binding PieSeries, RelativeSource={RelativeSource AncestorType=UserControl}}"
                              LegendPosition="{Binding LegendPosition, RelativeSource={RelativeSource AncestorType=UserControl}}"
                              Visibility="{Binding IsPieChart, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                <!-- رسالة عدم وجود بيانات -->
                <StackPanel VerticalAlignment="Center"
                            HorizontalAlignment="Center"
                            Visibility="{Binding HasNoData, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}">
                    
                    <TextBlock Text="📊"
                               FontSize="48"
                               HorizontalAlignment="Center"
                               Foreground="#E0E0E0"
                               Margin="0,0,0,12"/>
                    
                    <TextBlock Text="لا توجد بيانات للعرض"
                               FontSize="16"
                               FontWeight="SemiBold"
                               Foreground="#95A5A6"
                               HorizontalAlignment="Center"
                               Margin="0,0,0,4"/>
                    
                    <TextBlock Text="تحقق من الفلاتر أو قم بتحديث البيانات"
                               FontSize="12"
                               Foreground="#BDC3C7"
                               HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- مؤشر التحميل -->
                <Grid Visibility="{Binding IsLoading, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}"
                      Background="#80FFFFFF">
                    
                    <StackPanel VerticalAlignment="Center"
                                HorizontalAlignment="Center">
                        
                        <ProgressBar IsIndeterminate="True"
                                     Width="100"
                                     Height="4"
                                     Margin="0,0,0,12"/>
                        
                        <TextBlock Text="جاري تحميل البيانات..."
                                   FontSize="14"
                                   Foreground="#7F8C8D"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Grid>

            <!-- معلومات إضافية -->
            <Grid Grid.Row="3" 
                  Margin="0,15,0,0"
                  Visibility="{Binding ShowFooter, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}">
                
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- إحصائيات سريعة -->
                <StackPanel Grid.Column="0" 
                            Orientation="Horizontal">
                    
                    <TextBlock Text="{Binding FooterText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                               FontSize="11"
                               Foreground="#95A5A6"/>
                </StackPanel>

                <!-- آخر تحديث -->
                <TextBlock Grid.Column="1"
                           Text="{Binding LastUpdated, RelativeSource={RelativeSource AncestorType=UserControl}, StringFormat='آخر تحديث: {0:HH:mm}'}"
                           FontSize="10"
                           Foreground="#BDC3C7"/>
            </Grid>
        </Grid>
    </Border>
</UserControl>
