using AqlanCenterProApp.Models.Dashboard;

namespace AqlanCenterProApp.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة الداشبورد
    /// </summary>
    public interface IDashboardService
    {
        /// <summary>
        /// الحصول على إحصائيات الداشبورد الشاملة
        /// </summary>
        Task<DashboardStatistics> GetDashboardStatisticsAsync();

        /// <summary>
        /// الحصول على بيانات الإيرادات الشهرية
        /// </summary>
        Task<List<MonthlyRevenueData>> GetMonthlyRevenueDataAsync(int months = 12);

        /// <summary>
        /// الحصول على توزيع أنواع العلاج
        /// </summary>
        Task<List<TreatmentTypeDistribution>> GetTreatmentTypeDistributionAsync();

        /// <summary>
        /// الحصول على إحصائيات الأطباء
        /// </summary>
        Task<List<Models.Dashboard.DoctorStatistics>> GetDoctorStatisticsAsync();

        /// <summary>
        /// الحصول على المواعيد القادمة
        /// </summary>
        Task<List<UpcomingAppointment>> GetUpcomingAppointmentsAsync(int days = 7);

        /// <summary>
        /// الحصول على التنبيهات الذكية
        /// </summary>
        Task<List<SmartAlert>> GetSmartAlertsAsync();

        /// <summary>
        /// الحصول على بيانات الأداء اليومي
        /// </summary>
        Task<List<DailyPerformanceData>> GetDailyPerformanceDataAsync(int days = 30);

        /// <summary>
        /// الحصول على إحصائيات المدفوعات
        /// </summary>
        Task<PaymentStatistics> GetPaymentStatisticsAsync();

        /// <summary>
        /// الحصول على إحصائيات المخزون
        /// </summary>
        Task<InventoryStatistics> GetInventoryStatisticsAsync();

        /// <summary>
        /// الحصول على بيانات المخطط البياني للإيرادات
        /// </summary>
        Task<List<ChartData>> GetRevenueChartDataAsync(string period = "monthly");

        /// <summary>
        /// الحصول على بيانات المخطط البياني للمرضى
        /// </summary>
        Task<List<ChartData>> GetPatientChartDataAsync(string period = "monthly");

        /// <summary>
        /// الحصول على بيانات المخطط البياني للمواعيد
        /// </summary>
        Task<List<ChartData>> GetAppointmentChartDataAsync(string period = "weekly");

        /// <summary>
        /// تحديث العملة المختارة
        /// </summary>
        Task UpdateSelectedCurrencyAsync(string currency);

        /// <summary>
        /// الحصول على معدل تحويل العملة
        /// </summary>
        decimal GetCurrencyConversionRate(string fromCurrency, string toCurrency);

        /// <summary>
        /// تحويل المبلغ إلى العملة المختارة
        /// </summary>
        decimal ConvertCurrency(decimal amount, string fromCurrency, string toCurrency);

        /// <summary>
        /// الحصول على رمز العملة
        /// </summary>
        string GetCurrencySymbol(string currency);

        /// <summary>
        /// تنسيق المبلغ مع العملة
        /// </summary>
        string FormatCurrency(decimal amount, string currency);

        /// <summary>
        /// إنشاء تنبيه ذكي جديد
        /// </summary>
        Task<SmartAlert> CreateSmartAlertAsync(string title, string message, string alertType, string priority = "عادي");

        /// <summary>
        /// وضع علامة مقروء على التنبيه
        /// </summary>
        Task MarkAlertAsReadAsync(int alertId);

        /// <summary>
        /// حذف التنبيه
        /// </summary>
        Task DeleteAlertAsync(int alertId);

        /// <summary>
        /// الحصول على إعدادات الداشبورد
        /// </summary>
        Task<DashboardSettings> GetDashboardSettingsAsync();

        /// <summary>
        /// حفظ إعدادات الداشبورد
        /// </summary>
        Task SaveDashboardSettingsAsync(DashboardSettings settings);

        /// <summary>
        /// تحديث البيانات تلقائياً
        /// </summary>
        Task RefreshDataAsync();

        /// <summary>
        /// التحقق من التنبيهات الجديدة
        /// </summary>
        Task CheckForNewAlertsAsync();

        /// <summary>
        /// الحصول على ملخص سريع للداشبورد
        /// </summary>
        Task<string> GetDashboardSummaryAsync();

        /// <summary>
        /// تصدير بيانات الداشبورد
        /// </summary>
        Task<byte[]> ExportDashboardDataAsync(string format = "PDF");

        /// <summary>
        /// الحصول على الإحصائيات المحسنة للمرضى
        /// </summary>
        Task<EnhancedPatientStatistics> GetEnhancedPatientStatisticsAsync();

        /// <summary>
        /// الحصول على الإحصائيات المحسنة للمواعيد
        /// </summary>
        Task<EnhancedAppointmentStatistics> GetEnhancedAppointmentStatisticsAsync();

        /// <summary>
        /// الحصول على الإحصائيات المحسنة للموظفين
        /// </summary>
        Task<EnhancedEmployeeStatistics> GetEnhancedEmployeeStatisticsAsync();

        /// <summary>
        /// الحصول على الإحصائيات المحسنة للأطباء
        /// </summary>
        Task<List<EnhancedDoctorStatistics>> GetEnhancedDoctorStatisticsAsync();

        /// <summary>
        /// الحصول على الإحصائيات المحسنة للإيرادات
        /// </summary>
        Task<EnhancedRevenueStatistics> GetEnhancedRevenueStatisticsAsync();

        /// <summary>
        /// الحصول على التنبيهات الذكية المحسنة
        /// </summary>
        Task<List<EnhancedSmartAlert>> GetEnhancedSmartAlertsAsync();

        /// <summary>
        /// الحصول على بيانات المخطط البياني المحسنة
        /// </summary>
        Task<List<EnhancedChartData>> GetEnhancedChartDataAsync(string chartType, string period = "monthly");

        /// <summary>
        /// الحصول على بيانات الأداء اليومي المحسنة
        /// </summary>
        Task<List<EnhancedDailyPerformanceData>> GetEnhancedDailyPerformanceDataAsync(int days = 30);

        /// <summary>
        /// الحصول على إعدادات العملة المتاحة
        /// </summary>
        Task<List<CurrencySettings>> GetAvailableCurrenciesAsync();

        /// <summary>
        /// تحويل المبلغ من عملة إلى أخرى
        /// </summary>
        Task<decimal> ConvertCurrencyAsync(decimal amount, string fromCurrency, string toCurrency);

        /// <summary>
        /// الحصول على إعدادات لوحة التحكم الشاملة
        /// </summary>
        Task<ComprehensiveDashboardSettings> GetComprehensiveDashboardSettingsAsync();

        /// <summary>
        /// حفظ إعدادات لوحة التحكم الشاملة
        /// </summary>
        Task SaveComprehensiveDashboardSettingsAsync(ComprehensiveDashboardSettings settings);

        /// <summary>
        /// الحصول على حالة لوحة التحكم
        /// </summary>
        Task<DashboardState> GetDashboardStateAsync();

        /// <summary>
        /// تحديث حالة لوحة التحكم
        /// </summary>
        Task UpdateDashboardStateAsync(DashboardState state);

        /// <summary>
        /// إنشاء تنبيه ذكي جديد
        /// </summary>
        Task<EnhancedSmartAlert> CreateSmartAlertAsync(string title, string message, AlertType type, AlertPriority priority = AlertPriority.Normal);

        /// <summary>
        /// وضع علامة مقروء على التنبيه
        /// </summary>
        Task MarkAlertAsReadAsync(int alertId);

        /// <summary>
        /// حذف التنبيه
        /// </summary>
        Task DeleteAlertAsync(int alertId);

        /// <summary>
        /// الحصول على إحصائيات الأداء
        /// </summary>
        Task<Dictionary<string, object>> GetPerformanceStatisticsAsync();

        /// <summary>
        /// تحسين الأداء وتنظيف الكاش
        /// </summary>
        Task OptimizePerformanceAsync();

        /// <summary>
        /// الحصول على تقرير شامل للوحة التحكم
        /// </summary>
        Task<string> GenerateComprehensiveReportAsync(string format = "HTML");

        /// <summary>
        /// إرسال تنبيه عبر الواتساب
        /// </summary>
        Task SendWhatsAppAlertAsync(string phoneNumber, string message);

        /// <summary>
        /// إرسال تنبيه عبر البريد الإلكتروني
        /// </summary>
        Task SendEmailAlertAsync(string email, string subject, string message);

        /// <summary>
        /// الحصول على إحصائيات الاستخدام
        /// </summary>
        Task<Dictionary<string, object>> GetUsageStatisticsAsync();

        /// <summary>
        /// تسجيل نشاط المستخدم
        /// </summary>
        Task LogUserActivityAsync(string userId, string activity, Dictionary<string, object> data = null);
    }
}
