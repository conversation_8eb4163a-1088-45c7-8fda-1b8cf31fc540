<UserControl x:Class="AqlanCenterProApp.Views.Dashboard.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:AqlanCenterProApp.Views.Dashboard"
             xmlns:controls="clr-namespace:AqlanCenterProApp.Views.Controls"
             xmlns:lvc="clr-namespace:LiveChartsCore.SkiaSharpView.WPF;assembly=LiveChartsCore.SkiaSharpView.WPF"
             xmlns:converters="clr-namespace:AqlanCenterProApp.Converters"
             mc:Ignorable="d"
             d:DesignHeight="1200"
             d:DesignWidth="1400"
             FlowDirection="RightToLeft"
             Background="#FAFBFC"
             Loaded="UserControl_Loaded"
             Unloaded="UserControl_Unloaded">

    <UserControl.Resources>
        <!-- المحولات -->
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter"/>
        <converters:ColorToBrushConverter x:Key="ColorToBrushConverter"/>
        <converters:CurrencyConverter x:Key="CurrencyConverter"/>
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

        <!-- تأثيرات الظل متوفرة في App.xaml -->

        <!-- تعريف الأنماط المحسنة -->
        <Style x:Key="HeaderTextStyle"
               TargetType="TextBlock">
            <Setter Property="FontSize"
                    Value="24"/>
            <Setter Property="FontWeight"
                    Value="Bold"/>
            <Setter Property="Foreground"
                    Value="#FF8C00"/>
            <Setter Property="Margin"
                    Value="0,0,0,20"/>
            <Setter Property="TextAlignment"
                    Value="Center"/>
        </Style>

        <Style x:Key="SubHeaderTextStyle"
               TargetType="TextBlock">
            <Setter Property="FontSize"
                    Value="18"/>
            <Setter Property="FontWeight"
                    Value="SemiBold"/>
            <Setter Property="Foreground"
                    Value="#87CEEB"/>
            <Setter Property="Margin"
                    Value="0,0,0,15"/>
        </Style>

        <Style x:Key="SectionHeaderStyle"
               TargetType="TextBlock">
            <Setter Property="FontSize"
                    Value="16"/>
            <Setter Property="FontWeight"
                    Value="SemiBold"/>
            <Setter Property="Foreground"
                    Value="#2C3E50"/>
            <Setter Property="Margin"
                    Value="0,0,0,12"/>
        </Style>

        <Style x:Key="SectionBorderStyle"
               TargetType="Border">
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="CornerRadius"
                    Value="12"/>
            <Setter Property="Padding"
                    Value="24"/>
            <Setter Property="Margin"
                    Value="0,0,0,24"/>
            <Setter Property="Effect"
                    Value="{StaticResource DropShadowEffect}"/>
            <Setter Property="BorderBrush"
                    Value="#E8E8E8"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
        </Style>

        <Style x:Key="StatCardStyle"
               TargetType="Border">
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="CornerRadius"
                    Value="16"/>
            <Setter Property="Padding"
                    Value="20"/>
            <Setter Property="Margin"
                    Value="8"/>
            <Setter Property="Effect"
                    Value="{StaticResource DropShadowEffect}"/>
            <Setter Property="BorderBrush"
                    Value="#F0F0F0"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
        </Style>

        <Style x:Key="ModernCardStyle"
               TargetType="Border">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0"
                                         EndPoint="1,1">
                        <GradientStop Color="White"
                                      Offset="0"/>
                        <GradientStop Color="#FAFBFC"
                                      Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="CornerRadius"
                    Value="16"/>
            <Setter Property="Padding"
                    Value="24"/>
            <Setter Property="Margin"
                    Value="12"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black"
                                      Direction="270"
                                      ShadowDepth="4"
                                      BlurRadius="12"
                                      Opacity="0.15"/>
                </Setter.Value>
            </Setter>
            <Setter Property="BorderBrush"
                    Value="#E8E8E8"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
        </Style>

        <!-- نمط زر الإجراءات السريعة المحسن -->
        <Style x:Key="QuickActionButtonStyle"
               TargetType="Button">
            <Setter Property="Background"
                    Value="#3498DB"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="24,16"/>
            <Setter Property="Margin"
                    Value="8"/>
            <Setter Property="FontWeight"
                    Value="SemiBold"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="Cursor"
                    Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="16"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="Black"
                                                  Direction="270"
                                                  ShadowDepth="2"
                                                  BlurRadius="8"
                                                  Opacity="0.2"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                     Value="True">
                                <Setter Property="Background"
                                        Value="#2980B9"/>
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.05"
                                                        ScaleY="1.05"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed"
                                     Value="True">
                                <Setter Property="Background"
                                        Value="#21618C"/>
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98"
                                                        ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط زر التحديث -->
        <Style x:Key="RefreshButtonStyle"
               TargetType="Button">
            <Setter Property="Background"
                    Value="#2ECC71"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="16,12"/>
            <Setter Property="Margin"
                    Value="8"/>
            <Setter Property="FontWeight"
                    Value="SemiBold"/>
            <Setter Property="FontSize"
                    Value="13"/>
            <Setter Property="Cursor"
                    Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="12"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="Black"
                                                  Direction="270"
                                                  ShadowDepth="1"
                                                  BlurRadius="6"
                                                  Opacity="0.15"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                     Value="True">
                                <Setter Property="Background"
                                        Value="#27AE60"/>
                            </Trigger>
                            <Trigger Property="IsPressed"
                                     Value="True">
                                <Setter Property="Background"
                                        Value="#229954"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط زر التصدير -->
        <Style x:Key="ExportButtonStyle"
               TargetType="Button">
            <Setter Property="Background"
                    Value="#E67E22"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="16,12"/>
            <Setter Property="Margin"
                    Value="8"/>
            <Setter Property="FontWeight"
                    Value="SemiBold"/>
            <Setter Property="FontSize"
                    Value="13"/>
            <Setter Property="Cursor"
                    Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="12"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="Black"
                                                  Direction="270"
                                                  ShadowDepth="1"
                                                  BlurRadius="6"
                                                  Opacity="0.15"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                     Value="True">
                                <Setter Property="Background"
                                        Value="#D35400"/>
                            </Trigger>
                            <Trigger Property="IsPressed"
                                     Value="True">
                                <Setter Property="Background"
                                        Value="#BA4A00"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <ControlTemplate TargetType="Button">
            <Border Background="{TemplateBinding Background}"
                    CornerRadius="12"
                    Effect="{StaticResource DropShadowEffect}">
                <ContentPresenter HorizontalAlignment="Center"
                                  VerticalAlignment="Center"/>
            </Border>
            <ControlTemplate.Triggers>
                <Trigger Property="IsMouseOver"
                         Value="True">
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="Black"
                                              Direction="270"
                                              ShadowDepth="6"
                                              BlurRadius="15"
                                              Opacity="0.3"/>
                        </Setter.Value>
                    </Setter>
                    <Setter Property="Background"
                            Value="#2980B9"/>
                </Trigger>
                <Trigger Property="IsPressed"
                         Value="True">
                    <Setter Property="Background"
                            Value="#21618C"/>
                </Trigger>
            </ControlTemplate.Triggers>
        </ControlTemplate>
        </Setter.Value>
        </Setter>
        </Style>

        <!-- نمط رسالة الخطأ -->
        <Style x:Key="ErrorMessageStyle"
               TargetType="Border">
            <Setter Property="Background"
                    Value="#FFEBEE"/>
            <Setter Property="BorderBrush"
                    Value="#F44336"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="CornerRadius"
                    Value="8"/>
            <Setter Property="Padding"
                    Value="15"/>
            <Setter Property="Margin"
                    Value="0,0,0,20"/>
        </Style>

        <!-- نمط مؤشر التحميل -->
        <Style x:Key="LoadingOverlayStyle"
               TargetType="Border">
            <Setter Property="Background"
                    Value="#80000000"/>
            <Setter Property="CornerRadius"
                    Value="12"/>
        </Style>

        <!-- نمط مؤشر الأداء -->
        <Style x:Key="PerformanceIndicatorStyle"
               TargetType="Border">
            <Setter Property="Background"
                    Value="#E8F5E8"/>
            <Setter Property="BorderBrush"
                    Value="#4CAF50"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="CornerRadius"
                    Value="6"/>
            <Setter Property="Padding"
                    Value="8,4"/>
            <Setter Property="Margin"
                    Value="5,0"/>
        </Style>

        <!-- نمط مؤشر الأداء البطيء -->
        <Style x:Key="SlowPerformanceIndicatorStyle"
               TargetType="Border"
               BasedOn="{StaticResource PerformanceIndicatorStyle}">
            <Setter Property="Background"
                    Value="#FFF3E0"/>
            <Setter Property="BorderBrush"
                    Value="#FF9800"/>
        </Style>

        <!-- نمط مؤشر الأداء الضعيف -->
        <Style x:Key="PoorPerformanceIndicatorStyle"
               TargetType="Border"
               BasedOn="{StaticResource PerformanceIndicatorStyle}">
            <Setter Property="Background"
                    Value="#FFEBEE"/>
            <Setter Property="BorderBrush"
                    Value="#F44336"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <!-- المحتوى الرئيسي -->
        <ScrollViewer VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Padding="20">
            <StackPanel>

                <!-- Header Section المحسن -->
                <Border Style="{StaticResource ModernCardStyle}"
                        Margin="0,0,0,32">
                    <Border.Background>
                        <LinearGradientBrush StartPoint="0,0"
                                EndPoint="1,1">
                            <GradientStop Color="#FF8C00"
                                    Offset="0"/>
                            <GradientStop Color="#87CEEB"
                                    Offset="1"/>
                        </LinearGradientBrush>
                    </Border.Background>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- شعار المركز -->
                        <Border Grid.Row="0"
                                Grid.Column="0"
                                Grid.RowSpan="2"
                                Background="White"
                                CornerRadius="50"
                                Width="80"
                                Height="80"
                                VerticalAlignment="Center"
                                Margin="0,0,20,0">
                            <Border.Effect>
                                <DropShadowEffect Color="Black"
                                        Direction="270"
                                        ShadowDepth="3"
                                        BlurRadius="10"
                                        Opacity="0.3"/>
                            </Border.Effect>
                            <TextBlock Text="🦷"
                                       FontSize="40"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"/>
                        </Border>

                        <!-- معلومات المركز -->
                        <StackPanel Grid.Row="0"
                                Grid.Column="1"
                                VerticalAlignment="Center">
                            <TextBlock Text="مركز الدكتور عقلان لتقويم وزراعة وتجميل الأسنان"
                                       FontSize="24"
                                       FontWeight="Bold"
                                       Foreground="White"
                                       TextWrapping="Wrap"/>
                            <TextBlock Text="Dr. Aqlan Center for Orthodontics, Implants &amp; Cosmetic Dentistry"
                                       FontSize="14"
                                       Foreground="White"
                                       Opacity="0.9"
                                       Margin="0,4,0,0"/>
                        </StackPanel>

                        <!-- أزرار التحكم -->
                        <StackPanel Grid.Row="0"
                                Grid.Column="2"
                                    Orientation="Horizontal"
                                    VerticalAlignment="Center">

                            <!-- اختيار العملة -->
                            <ComboBox ItemsSource="{Binding AvailableCurrencies}"
                                      SelectedItem="{Binding SelectedCurrency}"
                                      DisplayMemberPath="Name"
                                      SelectedValuePath="Code"
                                      Width="120"
                                      Height="32"
                                      Margin="0,0,12,0"
                                      Background="White"
                                      FontSize="12"/>

                            <!-- زر التحديث -->
                            <Button Style="{StaticResource RefreshButtonStyle}"
                                    Command="{Binding RefreshDataCommand}"
                                    Content="🔄 تحديث"
                                    ToolTip="تحديث البيانات"/>

                            <!-- زر التصدير -->
                            <Button Style="{StaticResource ExportButtonStyle}"
                                    Command="{Binding ExportDashboardCommand}"
                                    Content="📊 تصدير"
                                    ToolTip="تصدير لوحة التحكم"/>
                        </StackPanel>

                        <!-- معلومات الحالة -->
                        <StackPanel Grid.Row="1"
                                Grid.Column="1"
                                Grid.ColumnSpan="2"
                                    Orientation="Horizontal"
                                    Margin="0,12,0,0">
                            <TextBlock Text="{Binding LastUpdated, StringFormat='آخر تحديث: {0:yyyy/MM/dd HH:mm}'}"
                                       FontSize="12"
                                       Foreground="White"
                                       Opacity="0.9"/>
                            <TextBlock Text=" • "
                                    FontSize="12"
                                    Foreground="White"
                                    Opacity="0.7"
                                    Margin="8,0"/>
                            <TextBlock Text="{Binding LoadTime, StringFormat='وقت التحميل: {0} مللي ثانية'}"
                                       FontSize="12"
                                       Foreground="White"
                                       Opacity="0.9"/>
                            <TextBlock Text=" • "
                                    FontSize="12"
                                    Foreground="White"
                                    Opacity="0.7"
                                    Margin="8,0"/>
                            <Border Background="#4CAF50"
                                    CornerRadius="8"
                                    Padding="6,2">
                                <TextBlock Text="{Binding PerformanceStatus}"
                                           FontSize="11"
                                           Foreground="White"
                                           FontWeight="SemiBold"/>
                            </Border>
                        </StackPanel>

                        <!-- مؤشر الأداء -->
                        <Border Style="{StaticResource PerformanceIndicatorStyle}"
                                Visibility="{Binding IsEssentialDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}">
                            <TextBlock Text="{Binding PerformanceStatus}"
                                       FontSize="12"
                                       Foreground="#2E7D32"
                                       FontWeight="SemiBold"/>
                        </Border>

                        <TextBlock Text=" | "
                                   FontSize="12"
                                   Foreground="White"
                                   Opacity="0.8"/>
                        <TextBlock Text="{Binding LoadTime, StringFormat='وقت التحميل: {0} مللي ثانية'}"
                                   FontSize="12"
                                   Foreground="White"
                                   Opacity="0.8"/>

                        <!-- مؤشرات إضافية -->
                        <TextBlock Text=" | "
                                   FontSize="12"
                                   Foreground="White"
                                   Opacity="0.8"
                                   Visibility="{Binding IsEssentialDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}"/>
                        <TextBlock Text="{Binding CacheHitCount, StringFormat='الكاش: {0}'}"
                                   FontSize="12"
                                   Foreground="White"
                                   Opacity="0.8"
                                   Visibility="{Binding IsEssentialDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}"/>

                        <TextBlock Text=" | "
                                   FontSize="12"
                                   Foreground="White"
                                   Opacity="0.8"
                                   Visibility="{Binding IsEssentialDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}"/>
                        <TextBlock Text="{Binding ErrorCount, StringFormat='الأخطاء: {0}'}"
                                   FontSize="12"
                                   Foreground="White"
                                   Opacity="0.8"
                                   Visibility="{Binding IsEssentialDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}"/>
            </StackPanel>
            </StackPanel>

            <StackPanel Grid.Column="1"
                        Orientation="Horizontal"
                        VerticalAlignment="Center">
                <!-- اختيار العملة -->
                <ComboBox SelectedItem="{Binding SelectedCurrency}"
                          Margin="0,0,10,0"
                          Width="100">
                    <ComboBoxItem Content="ريال يمني"
                                  Tag="YER"/>
                    <ComboBoxItem Content="ريال سعودي"
                                  Tag="SAR"/>
                    <ComboBoxItem Content="دولار أمريكي"
                                  Tag="USD"/>
                </ComboBox>

                <!-- زر التحديث -->
                <Border Background="#3498DB"
                        CornerRadius="6">
                    <Button Command="{Binding RefreshDataCommand}"
                            Background="Transparent"
                            Foreground="White"
                            Padding="15,8"
                            BorderThickness="0"
                            IsEnabled="{Binding IsRefreshing, Converter={StaticResource InverseBooleanConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄"
                                       FontSize="14"
                                       Margin="0,0,5,0"/>
                            <TextBlock Text="تحديث"
                                       FontWeight="SemiBold"/>
                        </StackPanel>
                    </Button>
                </Border>

                <!-- زر إعادة تعبئة البيانات التجريبية -->
                <Border Background="#FF6B35"
                        CornerRadius="6"
                        Margin="10,0,0,0">
                    <Button x:Name="ReseedDataButton"
                            Background="Transparent"
                            Foreground="White"
                            Padding="15,8"
                            BorderThickness="0"
                            Click="ReseedDataButton_Click"
                            ToolTip="إعادة تعبئة البيانات التجريبية للداشبورد">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🗂️"
                                       FontSize="14"
                                       Margin="0,0,5,0"/>
                            <TextBlock Text="بيانات تجريبية"
                                       FontWeight="SemiBold"/>
                        </StackPanel>
                    </Button>
                </Border>
            </StackPanel>
    </Grid>
    </Border>

    <!-- رسالة الخطأ -->
    <Border Style="{StaticResource ErrorMessageStyle}"
            Visibility="{Binding HasError, Converter={StaticResource BoolToVisibilityConverter}}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0"
                       Text="⚠️"
                       FontSize="20"
                       VerticalAlignment="Center"
                       Margin="0,0,10,0"/>

            <TextBlock Grid.Column="1"
                       Text="{Binding ErrorMessage}"
                       FontSize="14"
                       Foreground="#D32F2F"
                       VerticalAlignment="Center"
                       TextWrapping="Wrap"/>

            <Border Grid.Column="2"
                    Background="#F44336"
                    CornerRadius="4"
                    Margin="10,0,0,0">
                <Button Content="إعادة المحاولة"
                        Command="{Binding RefreshDataCommand}"
                        Background="Transparent"
                        Foreground="White"
                        BorderThickness="0"
                        Padding="10,5"/>
            </Border>
        </Grid>
    </Border>

    <!-- مؤشر التحميل الرئيسي -->
    <Border Style="{StaticResource LoadingOverlayStyle}"
            Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
        <StackPanel HorizontalAlignment="Center"
                    VerticalAlignment="Center">
            <ProgressBar IsIndeterminate="True"
                         Width="100"
                         Height="4"
                         Margin="0,0,0,10"/>
            <TextBlock Text="{Binding LoadingMessage}"
                       Foreground="White"
                       FontSize="16"
                       HorizontalAlignment="Center"/>
            <TextBlock Text="يرجى الانتظار..."
                       Foreground="White"
                       FontSize="12"
                       Opacity="0.8"
                       HorizontalAlignment="Center"
                       Margin="0,5,0,0"/>
        </StackPanel>
    </Border>

    <!-- مؤشر تحميل البيانات الثانوية -->
    <Border Background="#FFF3E0"
            BorderBrush="#FF9800"
            BorderThickness="1"
            CornerRadius="8"
            Padding="15"
            Margin="0,0,0,20"
            Visibility="{Binding IsEssentialDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0"
                       Text="📊"
                       FontSize="20"
                       VerticalAlignment="Center"
                       Margin="0,0,10,0"/>

            <StackPanel Grid.Column="1">
                <TextBlock Text="جاري تحميل المخططات والإحصائيات التفصيلية..."
                           FontSize="14"
                           FontWeight="SemiBold"
                           Foreground="#E65100"/>
                <TextBlock Text="سيتم تحديث المخططات والبيانات التفصيلية قريباً"
                           FontSize="12"
                           Foreground="#FF9800"
                           Margin="0,2,0,0"/>
            </StackPanel>
        </Grid>
    </Border>

    <!-- Statistics Cards Section -->
    <TextBlock Text="الإحصائيات السريعة"
               FontSize="20"
               FontWeight="Bold"
               Foreground="#3498DB"
               Margin="0,0,0,15"
               Visibility="{Binding IsEssentialDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}"/>

    <UniformGrid Columns="4"
                 Margin="0,0,0,30"
                 Visibility="{Binding IsEssentialDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}">

        <!-- إجمالي المرضى -->
        <controls:StatisticCard Title="إجمالي المرضى"
                                Value="{Binding Statistics.TotalPatients}"
                                Icon="👥"
                                SubText="المرضى المسجلين في النظام"/>

        <!-- المرضى الجدد -->
        <controls:StatisticCard Title="مرضى جدد هذا الشهر"
                                Value="{Binding Statistics.NewPatientsThisMonth}"
                                Icon="🆕"
                                SubText="مرضى مسجلين حديثاً"/>

        <!-- مواعيد اليوم -->
        <controls:StatisticCard Title="مواعيد اليوم"
                                Value="{Binding Statistics.TodayAppointments}"
                                Icon="📅"
                                SubText="مواعيد مجدولة اليوم"/>

        <!-- الإيرادات الشهرية -->
        <controls:StatisticCard Title="إيرادات الشهر"
                                Value="{Binding Statistics.ThisMonthRevenue, StringFormat='{}{0:N0}'}"
                                Icon="💰"
                                SubText="إجمالي الإيرادات"/>

    </UniformGrid>

    <UniformGrid Columns="4"
                 Margin="0,0,0,30"
                 Visibility="{Binding IsEssentialDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}">

        <!-- المدفوعات المتأخرة -->
        <controls:StatisticCard Title="مدفوعات متأخرة"
                                Value="{Binding Statistics.TotalOutstandingPayments, StringFormat='{}{0:N0}'}"
                                Icon="⚠️"
                                SubText="مبالغ مستحقة الدفع"/>

        <!-- المخزون المنخفض -->
        <controls:StatisticCard Title="مخزون منخفض"
                                Value="{Binding Statistics.LowStockItems}"
                                Icon="📦"
                                SubText="أصناف تحتاج تجديد"/>

        <!-- الأطباء النشطين -->
        <controls:StatisticCard Title="الأطباء النشطين"
                                Value="{Binding Statistics.ActiveDoctors}"
                                Icon="👨‍⚕️"
                                SubText="من إجمالي الأطباء"/>

        <!-- صافي الربح -->
        <controls:StatisticCard Title="صافي الربح"
                                Value="{Binding Statistics.NetProfitThisMonth, StringFormat='{}{0:N0}'}"
                                Icon="📈"
                                SubText="الربح الشهري"/>

    </UniformGrid>

    <!-- التنبيهات الذكية -->
    <TextBlock Text="التنبيهات الذكية"
               FontSize="20"
               FontWeight="Bold"
               Foreground="#2C3E50"
               Margin="0,0,0,15"
               Visibility="{Binding IsEssentialDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}"/>

    <Border Style="{StaticResource SectionBorderStyle}"
            Visibility="{Binding IsEssentialDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}">
        <ItemsControl ItemsSource="{Binding SmartAlerts}">
            <ItemsControl.ItemTemplate>
                <DataTemplate>
                    <Border Background="{Binding Color, Converter={StaticResource ColorToBrushConverter}}"
                            CornerRadius="8"
                            Padding="15"
                            Margin="0,0,0,10"
                            Opacity="0.1">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0"
                                       Text="{Binding Icon}"
                                       FontSize="20"
                                       VerticalAlignment="Center"
                                       Margin="0,0,15,0"/>

                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding Title}"
                                           FontWeight="Bold"
                                           FontSize="14"
                                           Foreground="#2C3E50"/>
                                <TextBlock Text="{Binding Message}"
                                           FontSize="12"
                                           Foreground="#7F8C8D"
                                           Margin="0,2,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2"
                                        Orientation="Horizontal">
                                <Button Command="{Binding DataContext.MarkAlertAsReadCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                        CommandParameter="{Binding}"
                                        Content="✓"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Foreground="#27AE60"
                                        FontWeight="Bold"
                                        Margin="5,0"/>
                                <Button Command="{Binding DataContext.DeleteAlertCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                        CommandParameter="{Binding}"
                                        Content="✕"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Foreground="#E74C3C"
                                        FontWeight="Bold"
                                        Margin="5,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </DataTemplate>
            </ItemsControl.ItemTemplate>
        </ItemsControl>
    </Border>

    <!-- الإجراءات السريعة -->
    <TextBlock Text="الإجراءات السريعة"
               FontSize="20"
               FontWeight="Bold"
               Foreground="#2C3E50"
               Margin="0,0,0,15"
               Visibility="{Binding IsEssentialDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}"/>

    <UniformGrid Columns="4"
                 Margin="0,0,0,30"
                 Visibility="{Binding IsEssentialDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}">

        <Button Style="{StaticResource QuickActionButtonStyle}"
                Background="#3498DB"
                Command="{Binding AddPatientCommand}">
            <StackPanel>
                <TextBlock Text="👤"
                           FontSize="20"
                           Margin="0,0,0,5"/>
                <TextBlock Text="مريض جديد"
                           FontWeight="Bold"/>
            </StackPanel>
        </Button>

        <Button Style="{StaticResource QuickActionButtonStyle}"
                Background="#2ECC71"
                Command="{Binding AddAppointmentCommand}">
            <StackPanel>
                <TextBlock Text="📅"
                           FontSize="20"
                           Margin="0,0,0,5"/>
                <TextBlock Text="موعد جديد"
                           FontWeight="Bold"/>
            </StackPanel>
        </Button>

        <Button Style="{StaticResource QuickActionButtonStyle}"
                Background="#E67E22"
                Command="{Binding AddInvoiceCommand}">
            <StackPanel>
                <TextBlock Text="💰"
                           FontSize="20"
                           Margin="0,0,0,5"/>
                <TextBlock Text="فاتورة جديدة"
                           FontWeight="Bold"/>
            </StackPanel>
        </Button>

        <Button Style="{StaticResource QuickActionButtonStyle}"
                Background="#9B59B6"
                Command="{Binding ViewReportsCommand}">
            <StackPanel>
                <TextBlock Text="📊"
                           FontSize="20"
                           Margin="0,0,0,5"/>
                <TextBlock Text="التقارير"
                           FontWeight="Bold"/>
            </StackPanel>
        </Button>

    </UniformGrid>

    <!-- المخططات البيانية -->
    <Grid Margin="0,0,0,30"
          Visibility="{Binding IsSecondaryDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- مخطط الإيرادات الشهرية -->
        <Border Grid.Column="0"
                Style="{StaticResource SectionBorderStyle}"
                Margin="0,0,10,0">
            <StackPanel>
                <TextBlock Text="الإيرادات الشهرية"
                           FontSize="16"
                           FontWeight="Bold"
                           Foreground="#2C3E50"
                           Margin="0,0,0,15"/>
                <lvc:CartesianChart Series="{Binding RevenueSeries}"
                                    XAxes="{Binding XAxes}"
                                    YAxes="{Binding YAxes}"
                                    Height="300"/>
            </StackPanel>
        </Border>

        <!-- مخطط توزيع أنواع العلاج -->
        <Border Grid.Column="1"
                Style="{StaticResource SectionBorderStyle}"
                Margin="10,0,0,0">
            <StackPanel>
                <TextBlock Text="توزيع أنواع العلاج"
                           FontSize="16"
                           FontWeight="Bold"
                           Foreground="#2C3E50"
                           Margin="0,0,0,15"/>
                <lvc:PieChart Series="{Binding TreatmentPieSeries}"
                              Height="300"/>
            </StackPanel>
        </Border>
    </Grid>

    <!-- المواعيد القادمة -->
    <TextBlock Text="المواعيد القادمة"
               FontSize="20"
               FontWeight="Bold"
               Foreground="#2C3E50"
               Margin="0,0,0,15"
               Visibility="{Binding IsEssentialDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}"/>

    <Border Style="{StaticResource SectionBorderStyle}"
            Visibility="{Binding IsEssentialDataLoaded, Converter={StaticResource BoolToVisibilityConverter}}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- عنوان الجدول -->
            <Border Grid.Row="0"
                    Background="#F8F9FA"
                    Padding="15,10"
                    Margin="-20,-20,-20,10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0"
                               Text="المريض"
                               FontWeight="Bold"
                               Foreground="#2C3E50"/>
                    <TextBlock Grid.Column="1"
                               Text="الطبيب"
                               FontWeight="Bold"
                               Foreground="#2C3E50"/>
                    <TextBlock Grid.Column="2"
                               Text="التاريخ والوقت"
                               FontWeight="Bold"
                               Foreground="#2C3E50"/>
                    <TextBlock Grid.Column="3"
                               Text="نوع العلاج"
                               FontWeight="Bold"
                               Foreground="#2C3E50"/>
                    <TextBlock Grid.Column="4"
                               Text="الحالة"
                               FontWeight="Bold"
                               Foreground="#2C3E50"/>
                    <TextBlock Grid.Column="5"
                               Text="إجراءات"
                               FontWeight="Bold"
                               Foreground="#2C3E50"/>
                </Grid>
            </Border>

            <!-- قائمة المواعيد -->
            <ScrollViewer Grid.Row="1"
                          VerticalScrollBarVisibility="Auto"
                          MaxHeight="400">
                <ItemsControl ItemsSource="{Binding UpcomingAppointments}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border BorderBrush="#E0E0E0"
                                    BorderThickness="0,0,0,1"
                                    Padding="0,10">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- معلومات المريض -->
                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="{Binding PatientName}"
                                                   FontWeight="SemiBold"
                                                   FontSize="14"
                                                   Foreground="#2C3E50"/>
                                        <TextBlock Text="{Binding PatientPhone}"
                                                   FontSize="12"
                                                   Foreground="#7F8C8D"/>
                                    </StackPanel>

                                    <!-- الطبيب -->
                                    <TextBlock Grid.Column="1"
                                               Text="{Binding DoctorName}"
                                               VerticalAlignment="Center"
                                               FontSize="13"/>

                                    <!-- التاريخ والوقت -->
                                    <StackPanel Grid.Column="2"
                                                VerticalAlignment="Center">
                                        <TextBlock Text="{Binding AppointmentDateTime, StringFormat='{}{0:yyyy/MM/dd}'}"
                                                   FontSize="13"/>
                                        <TextBlock Text="{Binding AppointmentDateTime, StringFormat='{}{0:HH:mm}'}"
                                                   FontSize="12"
                                                   Foreground="#7F8C8D"/>
                                    </StackPanel>

                                    <!-- نوع العلاج -->
                                    <TextBlock Grid.Column="3"
                                               Text="{Binding TreatmentType}"
                                               VerticalAlignment="Center"
                                               FontSize="13"/>

                                    <!-- الحالة -->
                                    <Border Grid.Column="4"
                                            CornerRadius="12"
                                            Padding="8,4"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsToday}"
                                                                 Value="True">
                                                        <Setter Property="Background"
                                                                Value="#E8F5E8"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsTomorrow}"
                                                                 Value="True">
                                                        <Setter Property="Background"
                                                                Value="#FFF3E0"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsOverdue}"
                                                                 Value="True">
                                                        <Setter Property="Background"
                                                                Value="#FFEBEE"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding Status}"
                                                   FontSize="11"
                                                   FontWeight="SemiBold">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsToday}"
                                                                     Value="True">
                                                            <Setter Property="Foreground"
                                                                    Value="#2E7D32"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding IsTomorrow}"
                                                                     Value="True">
                                                            <Setter Property="Foreground"
                                                                    Value="#F57C00"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding IsOverdue}"
                                                                     Value="True">
                                                            <Setter Property="Foreground"
                                                                    Value="#C62828"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>

                                    <!-- أزرار الإجراءات -->
                                    <StackPanel Grid.Column="5"
                                                Orientation="Horizontal"
                                                VerticalAlignment="Center">
                                        <Border Background="#27AE60"
                                                CornerRadius="4"
                                                Margin="2">
                                            <Button Command="{Binding DataContext.CallPatientCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding PatientPhone}"
                                                    Background="Transparent"
                                                    Foreground="White"
                                                    BorderThickness="0"
                                                    Padding="8,4"
                                                    ToolTip="اتصال">
                                                <TextBlock Text="📞"
                                                           FontSize="12"/>
                                            </Button>
                                        </Border>
                                        <Border Background="#25D366"
                                                CornerRadius="4"
                                                Margin="2">
                                            <Button Command="{Binding DataContext.SendWhatsAppCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding PatientPhone}"
                                                    Background="Transparent"
                                                    Foreground="White"
                                                    BorderThickness="0"
                                                    Padding="8,4"
                                                    ToolTip="واتساب">
                                                <TextBlock Text="💬"
                                                           FontSize="12"/>
                                            </Button>
                                        </Border>
                                    </StackPanel>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </Grid>
    </Border>

    </StackPanel>
    </ScrollViewer>
    </Grid>
</UserControl>
