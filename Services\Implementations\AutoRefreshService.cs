using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using AqlanCenterProApp.Services.Interfaces;

namespace AqlanCenterProApp.Services.Implementations
{
    /// <summary>
    /// خدمة التحديث التلقائي للوحة التحكم
    /// </summary>
    public class AutoRefreshService : BackgroundService
    {
        private readonly IDashboardService _dashboardService;
        private readonly ILogger<AutoRefreshService> _logger;
        private readonly Timer _refreshTimer;
        private readonly object _lockObject = new object();
        
        private bool _isRefreshing = false;
        private DateTime _lastRefresh = DateTime.Now;
        private int _refreshInterval = 300000; // 5 دقائق بالمللي ثانية
        private bool _isEnabled = true;

        public AutoRefreshService(
            IDashboardService dashboardService,
            ILogger<AutoRefreshService> logger)
        {
            _dashboardService = dashboardService ?? throw new ArgumentNullException(nameof(dashboardService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// بدء الخدمة
        /// </summary>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("بدء خدمة التحديث التلقائي للوحة التحكم");

            while (!stoppingToken.IsCancellationRequested && _isEnabled)
            {
                try
                {
                    await RefreshDashboardDataAsync();
                    await Task.Delay(_refreshInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("تم إيقاف خدمة التحديث التلقائي");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "خطأ في خدمة التحديث التلقائي");
                    await Task.Delay(30000, stoppingToken); // انتظار 30 ثانية قبل المحاولة مرة أخرى
                }
            }
        }

        /// <summary>
        /// تحديث بيانات لوحة التحكم
        /// </summary>
        private async Task RefreshDashboardDataAsync()
        {
            lock (_lockObject)
            {
                if (_isRefreshing)
                {
                    _logger.LogDebug("التحديث قيد التنفيذ بالفعل، تم تخطي هذه الدورة");
                    return;
                }
                _isRefreshing = true;
            }

            try
            {
                var startTime = DateTime.Now;
                _logger.LogDebug("بدء تحديث بيانات لوحة التحكم");

                // تحديث البيانات الأساسية
                await RefreshEssentialDataAsync();

                // تحديث البيانات الثانوية
                await RefreshSecondaryDataAsync();

                // تحديث التنبيهات
                await RefreshAlertsAsync();

                // تنظيف الكاش القديم
                await CleanupOldCacheAsync();

                var endTime = DateTime.Now;
                var duration = endTime - startTime;
                
                _lastRefresh = endTime;
                _logger.LogInformation($"تم تحديث بيانات لوحة التحكم بنجاح في {duration.TotalMilliseconds:F0} مللي ثانية");

                // إثارة حدث التحديث
                DataRefreshed?.Invoke(this, new DataRefreshedEventArgs
                {
                    RefreshTime = endTime,
                    Duration = duration,
                    Success = true
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث بيانات لوحة التحكم");
                
                DataRefreshed?.Invoke(this, new DataRefreshedEventArgs
                {
                    RefreshTime = DateTime.Now,
                    Duration = TimeSpan.Zero,
                    Success = false,
                    Error = ex.Message
                });
            }
            finally
            {
                lock (_lockObject)
                {
                    _isRefreshing = false;
                }
            }
        }

        /// <summary>
        /// تحديث البيانات الأساسية
        /// </summary>
        private async Task RefreshEssentialDataAsync()
        {
            try
            {
                var tasks = new[]
                {
                    _dashboardService.GetDashboardStatisticsAsync(),
                    _dashboardService.GetEnhancedPatientStatisticsAsync(),
                    _dashboardService.GetEnhancedAppointmentStatisticsAsync(),
                    _dashboardService.GetEnhancedRevenueStatisticsAsync()
                };

                await Task.WhenAll(tasks);
                _logger.LogDebug("تم تحديث البيانات الأساسية");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "خطأ في تحديث البيانات الأساسية");
            }
        }

        /// <summary>
        /// تحديث البيانات الثانوية
        /// </summary>
        private async Task RefreshSecondaryDataAsync()
        {
            try
            {
                var tasks = new[]
                {
                    _dashboardService.GetEnhancedEmployeeStatisticsAsync(),
                    _dashboardService.GetEnhancedDoctorStatisticsAsync(),
                    _dashboardService.GetInventoryStatisticsAsync(),
                    _dashboardService.GetPaymentStatisticsAsync()
                };

                await Task.WhenAll(tasks);
                _logger.LogDebug("تم تحديث البيانات الثانوية");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "خطأ في تحديث البيانات الثانوية");
            }
        }

        /// <summary>
        /// تحديث التنبيهات
        /// </summary>
        private async Task RefreshAlertsAsync()
        {
            try
            {
                await _dashboardService.GetEnhancedSmartAlertsAsync();
                _logger.LogDebug("تم تحديث التنبيهات");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "خطأ في تحديث التنبيهات");
            }
        }

        /// <summary>
        /// تنظيف الكاش القديم
        /// </summary>
        private async Task CleanupOldCacheAsync()
        {
            try
            {
                await _dashboardService.OptimizePerformanceAsync();
                _logger.LogDebug("تم تنظيف الكاش القديم");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "خطأ في تنظيف الكاش");
            }
        }

        /// <summary>
        /// تعيين فترة التحديث
        /// </summary>
        public void SetRefreshInterval(int intervalInSeconds)
        {
            if (intervalInSeconds < 30)
            {
                throw new ArgumentException("فترة التحديث يجب أن تكون 30 ثانية على الأقل");
            }

            _refreshInterval = intervalInSeconds * 1000;
            _logger.LogInformation($"تم تعيين فترة التحديث إلى {intervalInSeconds} ثانية");
        }

        /// <summary>
        /// تفعيل أو تعطيل التحديث التلقائي
        /// </summary>
        public void SetEnabled(bool enabled)
        {
            _isEnabled = enabled;
            _logger.LogInformation($"تم {(enabled ? "تفعيل" : "تعطيل")} التحديث التلقائي");
        }

        /// <summary>
        /// تحديث فوري
        /// </summary>
        public async Task ForceRefreshAsync()
        {
            _logger.LogInformation("طلب تحديث فوري للبيانات");
            await RefreshDashboardDataAsync();
        }

        /// <summary>
        /// الحصول على معلومات الحالة
        /// </summary>
        public RefreshServiceStatus GetStatus()
        {
            return new RefreshServiceStatus
            {
                IsEnabled = _isEnabled,
                IsRefreshing = _isRefreshing,
                LastRefresh = _lastRefresh,
                RefreshInterval = TimeSpan.FromMilliseconds(_refreshInterval),
                NextRefresh = _lastRefresh.AddMilliseconds(_refreshInterval)
            };
        }

        /// <summary>
        /// حدث التحديث
        /// </summary>
        public event EventHandler<DataRefreshedEventArgs> DataRefreshed;

        /// <summary>
        /// إيقاف الخدمة
        /// </summary>
        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("إيقاف خدمة التحديث التلقائي");
            await base.StopAsync(cancellationToken);
        }

        /// <summary>
        /// تحرير الموارد
        /// </summary>
        public override void Dispose()
        {
            _refreshTimer?.Dispose();
            base.Dispose();
        }
    }

    /// <summary>
    /// معلومات حالة خدمة التحديث
    /// </summary>
    public class RefreshServiceStatus
    {
        public bool IsEnabled { get; set; }
        public bool IsRefreshing { get; set; }
        public DateTime LastRefresh { get; set; }
        public TimeSpan RefreshInterval { get; set; }
        public DateTime NextRefresh { get; set; }
    }

    /// <summary>
    /// معلومات حدث التحديث
    /// </summary>
    public class DataRefreshedEventArgs : EventArgs
    {
        public DateTime RefreshTime { get; set; }
        public TimeSpan Duration { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
    }
}
