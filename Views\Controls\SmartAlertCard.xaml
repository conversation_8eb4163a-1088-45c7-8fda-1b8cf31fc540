<UserControl x:Class="AqlanCenterProApp.Views.Controls.SmartAlertCard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="80"
             d:DesignWidth="400"
             FlowDirection="RightToLeft">

    <Border CornerRadius="8"
            Margin="4"
            Padding="12"
            Background="{Binding AlertBackground, RelativeSource={RelativeSource AncestorType=UserControl}}"
            BorderBrush="{Binding AlertBorderColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
            BorderThickness="1"
            Effect="{StaticResource DropShadowEffect}">
        
        <Border.Style>
            <Style TargetType="Border">
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="Black"
                                                  Direction="270"
                                                  ShadowDepth="4"
                                                  BlurRadius="10"
                                                  Opacity="0.2"/>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>
        </Border.Style>

        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- أيقونة التنبيه -->
            <Border Grid.Column="0"
                    Background="{Binding IconBackground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                    CornerRadius="20"
                    Width="40"
                    Height="40"
                    VerticalAlignment="Center"
                    Margin="0,0,12,0">

                <TextBlock Text="{Binding AlertIcon, RelativeSource={RelativeSource AncestorType=UserControl}}"
                           FontSize="18"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Foreground="White"
                           FontWeight="Bold"/>
            </Border>

            <!-- محتوى التنبيه -->
            <StackPanel Grid.Column="1"
                        VerticalAlignment="Center">
                
                <TextBlock Text="{Binding AlertTitle, RelativeSource={RelativeSource AncestorType=UserControl}}"
                           FontSize="14"
                           FontWeight="SemiBold"
                           Foreground="#2C3E50"
                           Margin="0,0,0,4"
                           TextWrapping="Wrap"/>

                <TextBlock Text="{Binding AlertMessage, RelativeSource={RelativeSource AncestorType=UserControl}}"
                           FontSize="12"
                           Foreground="#7F8C8D"
                           TextWrapping="Wrap"
                           LineHeight="16"/>
            </StackPanel>

            <!-- وقت التنبيه -->
            <TextBlock Grid.Column="2"
                       Text="{Binding AlertTime, RelativeSource={RelativeSource AncestorType=UserControl}}"
                       FontSize="10"
                       Foreground="#95A5A6"
                       VerticalAlignment="Top"
                       Margin="8,0,8,0"/>

            <!-- أزرار الإجراءات -->
            <StackPanel Grid.Column="3"
                        Orientation="Horizontal"
                        VerticalAlignment="Center">

                <!-- زر الإجراء -->
                <Button Content="{Binding ActionText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                        Command="{Binding ActionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                        FontSize="11"
                        Padding="8,4"
                        Margin="0,0,4,0"
                        Background="{Binding IconBackground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                        Foreground="White"
                        BorderThickness="0"
                        CornerRadius="4"
                        Visibility="{Binding ShowAction, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}">
                    
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="4"
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="Cursor" Value="Hand"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Opacity" Value="0.8"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>

                <!-- زر الإغلاق -->
                <Button Content="✕"
                        Command="{Binding CloseCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                        FontSize="12"
                        Width="24"
                        Height="24"
                        Background="Transparent"
                        Foreground="#95A5A6"
                        BorderThickness="0"
                        VerticalAlignment="Top">
                    
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="12">
                                            <ContentPresenter HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="Cursor" Value="Hand"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#E74C3C"/>
                                    <Setter Property="Foreground" Value="White"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </StackPanel>

            <!-- مؤشر الأولوية -->
            <Rectangle Grid.Column="0"
                       Grid.ColumnSpan="4"
                       Width="4"
                       HorizontalAlignment="Right"
                       Fill="{Binding PriorityColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                       Margin="0,0,-12,0"/>

        </Grid>
    </Border>
</UserControl>
