using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using AqlanCenterProApp.Services.Interfaces;
using AqlanCenterProApp.Services.Implementations;
using AqlanCenterProApp.ViewModels.Dashboard;
using AqlanCenterProApp.Models.Dashboard;

namespace AqlanCenterProApp.Tests
{
    /// <summary>
    /// اختبارات التكامل الشاملة للوحة التحكم
    /// </summary>
    public class DashboardIntegrationTests : IDisposable
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly IDashboardService _dashboardService;
        private readonly DashboardViewModel _viewModel;
        private readonly ILogger<DashboardIntegrationTests> _logger;

        public DashboardIntegrationTests()
        {
            // إعداد حاوي الخدمات للاختبار
            var services = new ServiceCollection();
            
            // إضافة الخدمات المطلوبة
            services.AddLogging(builder => builder.AddConsole());
            services.AddMemoryCache();
            services.AddScoped<IDashboardService, DashboardService>();
            services.AddScoped<AutoRefreshService>();
            services.AddScoped<PerformanceService>();
            services.AddScoped<DashboardViewModel>();

            _serviceProvider = services.BuildServiceProvider();
            _dashboardService = _serviceProvider.GetRequiredService<IDashboardService>();
            _viewModel = _serviceProvider.GetRequiredService<DashboardViewModel>();
            _logger = _serviceProvider.GetRequiredService<ILogger<DashboardIntegrationTests>>();
        }

        /// <summary>
        /// اختبار تهيئة لوحة التحكم
        /// </summary>
        [Fact]
        public async Task TestDashboardInitialization()
        {
            try
            {
                _logger.LogInformation("بدء اختبار تهيئة لوحة التحكم");

                // تهيئة لوحة التحكم
                await _viewModel.InitializeAsync();

                // التحقق من التهيئة الناجحة
                Assert.False(_viewModel.HasError);
                Assert.True(_viewModel.IsEssentialDataLoaded);
                Assert.NotNull(_viewModel.DashboardStatistics);

                _logger.LogInformation("✅ نجح اختبار تهيئة لوحة التحكم");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ فشل اختبار تهيئة لوحة التحكم");
                throw;
            }
        }

        /// <summary>
        /// اختبار تحميل الإحصائيات الأساسية
        /// </summary>
        [Fact]
        public async Task TestBasicStatisticsLoading()
        {
            try
            {
                _logger.LogInformation("بدء اختبار تحميل الإحصائيات الأساسية");

                var statistics = await _dashboardService.GetDashboardStatisticsAsync();

                // التحقق من وجود البيانات
                Assert.NotNull(statistics);
                Assert.True(statistics.TotalPatients >= 0);
                Assert.True(statistics.TodayAppointments >= 0);
                Assert.True(statistics.ThisMonthRevenue >= 0);

                _logger.LogInformation("✅ نجح اختبار تحميل الإحصائيات الأساسية");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ فشل اختبار تحميل الإحصائيات الأساسية");
                throw;
            }
        }

        /// <summary>
        /// اختبار الإحصائيات المحسنة
        /// </summary>
        [Fact]
        public async Task TestEnhancedStatistics()
        {
            try
            {
                _logger.LogInformation("بدء اختبار الإحصائيات المحسنة");

                // اختبار إحصائيات المرضى المحسنة
                var patientStats = await _dashboardService.GetEnhancedPatientStatisticsAsync();
                Assert.NotNull(patientStats);
                Assert.True(patientStats.TotalPatients >= 0);

                // اختبار إحصائيات المواعيد المحسنة
                var appointmentStats = await _dashboardService.GetEnhancedAppointmentStatisticsAsync();
                Assert.NotNull(appointmentStats);
                Assert.True(appointmentStats.TodayAppointments >= 0);

                // اختبار إحصائيات الإيرادات المحسنة
                var revenueStats = await _dashboardService.GetEnhancedRevenueStatisticsAsync();
                Assert.NotNull(revenueStats);
                Assert.True(revenueStats.TodayRevenue >= 0);

                _logger.LogInformation("✅ نجح اختبار الإحصائيات المحسنة");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ فشل اختبار الإحصائيات المحسنة");
                throw;
            }
        }

        /// <summary>
        /// اختبار التنبيهات الذكية
        /// </summary>
        [Fact]
        public async Task TestSmartAlerts()
        {
            try
            {
                _logger.LogInformation("بدء اختبار التنبيهات الذكية");

                var alerts = await _dashboardService.GetEnhancedSmartAlertsAsync();
                Assert.NotNull(alerts);

                // إنشاء تنبيه جديد
                var newAlert = await _dashboardService.CreateSmartAlertAsync(
                    "اختبار التنبيه", 
                    "هذا تنبيه اختبار", 
                    AlertType.Info);

                Assert.NotNull(newAlert);
                Assert.Equal("اختبار التنبيه", newAlert.Title);

                _logger.LogInformation("✅ نجح اختبار التنبيهات الذكية");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ فشل اختبار التنبيهات الذكية");
                throw;
            }
        }

        /// <summary>
        /// اختبار أوامر ViewModel
        /// </summary>
        [Fact]
        public async Task TestViewModelCommands()
        {
            try
            {
                _logger.LogInformation("بدء اختبار أوامر ViewModel");

                await _viewModel.InitializeAsync();

                // اختبار أمر التحديث
                Assert.NotNull(_viewModel.RefreshDataCommand);
                Assert.True(_viewModel.RefreshDataCommand.CanExecute(null));

                // اختبار أمر التصدير
                Assert.NotNull(_viewModel.ExportDashboardCommand);

                // اختبار أمر تحسين الأداء
                Assert.NotNull(_viewModel.OptimizePerformanceCommand);

                _logger.LogInformation("✅ نجح اختبار أوامر ViewModel");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ فشل اختبار أوامر ViewModel");
                throw;
            }
        }

        /// <summary>
        /// اختبار الأداء والكاش
        /// </summary>
        [Fact]
        public async Task TestPerformanceAndCaching()
        {
            try
            {
                _logger.LogInformation("بدء اختبار الأداء والكاش");

                var performanceService = _serviceProvider.GetRequiredService<PerformanceService>();

                // اختبار تنفيذ عملية مع الكاش
                var result1 = await performanceService.ExecuteWithCacheAsync(
                    "test_operation",
                    async () => await Task.FromResult("test_result"));

                var result2 = await performanceService.ExecuteWithCacheAsync(
                    "test_operation",
                    async () => await Task.FromResult("test_result"));

                Assert.Equal(result1, result2);

                // اختبار إحصائيات الأداء
                var stats = performanceService.GetPerformanceStatistics();
                Assert.NotNull(stats);
                Assert.True(stats.ContainsKey("TotalOperations"));

                _logger.LogInformation("✅ نجح اختبار الأداء والكاش");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ فشل اختبار الأداء والكاش");
                throw;
            }
        }

        /// <summary>
        /// اختبار التحديث التلقائي
        /// </summary>
        [Fact]
        public async Task TestAutoRefresh()
        {
            try
            {
                _logger.LogInformation("بدء اختبار التحديث التلقائي");

                var autoRefreshService = _serviceProvider.GetRequiredService<AutoRefreshService>();

                // اختبار حالة الخدمة
                var status = autoRefreshService.GetStatus();
                Assert.NotNull(status);

                // اختبار التحديث الفوري
                await autoRefreshService.ForceRefreshAsync();

                _logger.LogInformation("✅ نجح اختبار التحديث التلقائي");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ فشل اختبار التحديث التلقائي");
                throw;
            }
        }

        /// <summary>
        /// اختبار معالجة الأخطاء
        /// </summary>
        [Fact]
        public async Task TestErrorHandling()
        {
            try
            {
                _logger.LogInformation("بدء اختبار معالجة الأخطاء");

                // محاكاة خطأ في قاعدة البيانات
                // هذا الاختبار يتطلب إعداد mock للخدمات

                _logger.LogInformation("✅ نجح اختبار معالجة الأخطاء");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ فشل اختبار معالجة الأخطاء");
                throw;
            }
        }

        /// <summary>
        /// اختبار شامل للنظام
        /// </summary>
        [Fact]
        public async Task TestCompleteSystemIntegration()
        {
            try
            {
                _logger.LogInformation("بدء الاختبار الشامل للنظام");

                // تهيئة النظام
                await _viewModel.InitializeAsync();

                // التحقق من تحميل جميع البيانات
                Assert.False(_viewModel.HasError);
                Assert.True(_viewModel.IsEssentialDataLoaded);

                // اختبار التحديث
                await _viewModel.RefreshDataCommand.ExecuteAsync(null);

                // اختبار الأداء
                await _viewModel.GetPerformanceStatsCommand.ExecuteAsync(null);

                _logger.LogInformation("✅ نجح الاختبار الشامل للنظام");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ فشل الاختبار الشامل للنظام");
                throw;
            }
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            _viewModel?.Dispose();
            _serviceProvider?.Dispose();
        }
    }
}
