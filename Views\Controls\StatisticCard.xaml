<UserControl x:Class="AqlanCenterProApp.Views.Controls.StatisticCard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="120"
             d:DesignWidth="250"
             FlowDirection="RightToLeft">

    <Border CornerRadius="12"
            Margin="8"
            Padding="20"
            Height="140"
            Background="{Binding CardBackground, RelativeSource={RelativeSource AncestorType=UserControl}}"
            Effect="{StaticResource DropShadowEffect}">
        <Border.Style>
            <Style TargetType="Border">
                <Style.Triggers>
                    <Trigger Property="IsMouseOver"
                             Value="True">
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="Black"
                                                  Direction="270"
                                                  ShadowDepth="8"
                                                  BlurRadius="15"
                                                  Opacity="0.3"/>
                            </Setter.Value>
                        </Setter>
                        <Setter Property="RenderTransform">
                            <Setter.Value>
                                <ScaleTransform ScaleX="1.02"
                                                ScaleY="1.02"/>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
                <Style.Resources>
                    <Storyboard x:Key="HoverAnimation">
                        <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                         To="1.02"
                                         Duration="0:0:0.2"/>
                        <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                         To="1.02"
                                         Duration="0:0:0.2"/>
                    </Storyboard>
                </Style.Resources>
            </Style>
        </Border.Style>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- الأيقونة المحسنة -->
            <Border Grid.Row="0"
                    Grid.Column="1"
                    Grid.RowSpan="2"
                    Background="{Binding IconBackground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                    CornerRadius="30"
                    Width="60"
                    Height="60"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Center">
                <Border.Effect>
                    <DropShadowEffect Color="Black"
                                      Direction="270"
                                      ShadowDepth="2"
                                      BlurRadius="8"
                                      Opacity="0.2"/>
                </Border.Effect>

                <TextBlock Text="{Binding Icon, RelativeSource={RelativeSource AncestorType=UserControl}}"
                           FontSize="28"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Foreground="White"
                           FontWeight="Bold"/>
            </Border>

            <!-- العنوان المحسن -->
            <TextBlock Grid.Row="0"
                       Grid.Column="0"
                       Text="{Binding Title, RelativeSource={RelativeSource AncestorType=UserControl}}"
                       FontSize="15"
                       FontWeight="SemiBold"
                       Foreground="#2C3E50"
                       Margin="0,0,15,8"
                       TextWrapping="Wrap"
                       LineHeight="18"/>

            <!-- القيمة الرئيسية المحسنة -->
            <StackPanel Grid.Row="1"
                        Grid.Column="0"
                        Orientation="Horizontal"
                        VerticalAlignment="Center"
                        Margin="0,0,15,0">

                <TextBlock Text="{Binding Value, RelativeSource={RelativeSource AncestorType=UserControl}}"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{Binding ValueColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                           VerticalAlignment="Center"/>

                <!-- وحدة القياس أو العملة -->
                <TextBlock Text="{Binding Unit, RelativeSource={RelativeSource AncestorType=UserControl}}"
                           FontSize="14"
                           FontWeight="Normal"
                           Foreground="#7F8C8D"
                           VerticalAlignment="Bottom"
                           Margin="5,0,0,5"
                           Visibility="{Binding ShowUnit, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StackPanel>

            <!-- النص الفرعي المحسن -->
            <TextBlock Grid.Row="2"
                       Grid.Column="0"
                       Grid.ColumnSpan="2"
                       Text="{Binding SubText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                       FontSize="12"
                       Foreground="#7F8C8D"
                       Margin="0,8,0,0"
                       TextWrapping="Wrap"
                       LineHeight="16"/>

            <!-- مؤشر التغيير المحسن -->
            <Border Grid.Row="2"
                    Grid.Column="1"
                    Background="{Binding TrendBackgroundColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                    CornerRadius="12"
                    Padding="8,4"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Bottom"
                    Margin="5,8,0,0"
                    Visibility="{Binding ShowTrend, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}">

                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="{Binding TrendIcon, RelativeSource={RelativeSource AncestorType=UserControl}}"
                               FontSize="14"
                               Foreground="{Binding TrendColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                               VerticalAlignment="Center"
                               FontWeight="Bold"/>

                    <TextBlock Text="{Binding TrendValue, RelativeSource={RelativeSource AncestorType=UserControl}}"
                               FontSize="12"
                               FontWeight="SemiBold"
                               Foreground="{Binding TrendColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                               Margin="4,0,0,0"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- شريط التقدم (اختياري) -->
            <ProgressBar Grid.Row="2"
                         Grid.Column="0"
                         Grid.ColumnSpan="2"
                         Height="4"
                         Margin="0,12,0,0"
                         VerticalAlignment="Bottom"
                         Background="#E8E8E8"
                         Foreground="{Binding IconBackground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                         Value="{Binding ProgressValue, RelativeSource={RelativeSource AncestorType=UserControl}}"
                         Maximum="100"
                         Visibility="{Binding ShowProgress, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}"/>

        </Grid>
    </Border>

    </Grid>

    <!-- تأثير الهوفر -->
    <Border.Style>
        <Style TargetType="Border">
            <Style.Triggers>
                <Trigger Property="IsMouseOver"
                         Value="True">
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="Black"
                                              Direction="270"
                                              ShadowDepth="8"
                                              BlurRadius="15"
                                              Opacity="0.3"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Border.Style>

    </Border>
</UserControl>
