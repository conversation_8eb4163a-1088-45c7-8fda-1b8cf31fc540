<UserControl x:Class="AqlanCenterProApp.Views.Controls.StatisticCard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="120"
             d:DesignWidth="250"
             FlowDirection="RightToLeft">

    <Border CornerRadius="12"
            Margin="8"
            Padding="20"
            Height="120"
            Effect="{StaticResource DropShadowEffect}">
        <Border.Background>
            <LinearGradientBrush StartPoint="0,0"
                    EndPoint="1,1">
                <GradientStop Color="White"
                        Offset="0"/>
                <GradientStop Color="#F8F9FA"
                        Offset="1"/>
            </LinearGradientBrush>
        </Border.Background>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- الأيقونة -->
            <Border Grid.Row="0"
                    Grid.Column="1"
                    Grid.RowSpan="2"
                    Background="{Binding IconBackground, RelativeSource={RelativeSource AncestorType=UserControl}}"
                    CornerRadius="25"
                    Width="50"
                    Height="50"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Center">

                <TextBlock Text="{Binding Icon, RelativeSource={RelativeSource AncestorType=UserControl}}"
                           FontSize="24"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Foreground="White"/>
            </Border>

            <!-- العنوان -->
            <TextBlock Grid.Row="0"
                       Grid.Column="0"
                       Text="{Binding Title, RelativeSource={RelativeSource AncestorType=UserControl}}"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Foreground="#3498DB"
                       Margin="0,0,10,5"
                       TextWrapping="Wrap"/>

            <!-- القيمة الرئيسية -->
            <TextBlock Grid.Row="1"
                       Grid.Column="0"
                       Text="{Binding Value, RelativeSource={RelativeSource AncestorType=UserControl}}"
                       FontSize="28"
                       FontWeight="Bold"
                       Foreground="{Binding ValueColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                       VerticalAlignment="Center"
                       Margin="0,0,10,0"/>

            <!-- النص الفرعي -->
            <TextBlock Grid.Row="2"
                       Grid.Column="0"
                       Grid.ColumnSpan="2"
                       Text="{Binding SubText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                       FontSize="11"
                       Foreground="#95A5A6"
                       Margin="0,5,0,0"
                       TextWrapping="Wrap"/>

            <!-- مؤشر التغيير -->
            <StackPanel Grid.Row="2"
                        Grid.Column="1"
                        Orientation="Horizontal"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Bottom"
                        Margin="5,5,0,0"
                        Visibility="{Binding ShowTrend, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}">

                <TextBlock Text="{Binding TrendIcon, RelativeSource={RelativeSource AncestorType=UserControl}}"
                           FontSize="12"
                           Foreground="{Binding TrendColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                           VerticalAlignment="Center"/>

                <TextBlock Text="{Binding TrendValue, RelativeSource={RelativeSource AncestorType=UserControl}}"
                           FontSize="11"
                           FontWeight="SemiBold"
                           Foreground="{Binding TrendColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                           Margin="2,0,0,0"
                           VerticalAlignment="Center"/>
            </StackPanel>

        </Grid>

        <!-- تأثير الهوفر -->
        <Border.Style>
            <Style TargetType="Border">
                <Style.Triggers>
                    <Trigger Property="IsMouseOver"
                             Value="True">
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="Black"
                                                  Direction="270"
                                                  ShadowDepth="8"
                                                  BlurRadius="15"
                                                  Opacity="0.3"/>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>
        </Border.Style>

    </Border>
</UserControl>
