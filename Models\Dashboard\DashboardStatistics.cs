using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models.Dashboard
{
    /// <summary>
    /// أنواع التنبيهات
    /// </summary>
    public enum AlertType
    {
        Info,
        Warning,
        Error,
        Success,
        Payment,
        Inventory,
        Appointment,
        System
    }

    /// <summary>
    /// نموذج إحصائيات الداشبورد الشاملة
    /// </summary>
    public class DashboardStatistics
    {
        // إحصائيات المرضى
        public int TotalPatients { get; set; }
        public int ActivePatients { get; set; }
        public int NewPatientsThisMonth { get; set; }
        public int NewPatientsToday { get; set; }
        public int ArchivedPatients { get; set; }
        public int PatientsWithOutstandingPayments { get; set; }

        // إحصائيات المواعيد
        public int TodayAppointments { get; set; }
        public int TomorrowAppointments { get; set; }
        public int ThisWeekAppointments { get; set; }
        public int CompletedAppointmentsToday { get; set; }
        public int CancelledAppointmentsToday { get; set; }
        public int MissedAppointmentsToday { get; set; }
        public int PendingAppointments { get; set; }
        public int CancelledAppointments { get; set; }

        // الإحصائيات المالية
        public decimal TodayRevenue { get; set; }
        public decimal ThisMonthRevenue { get; set; }
        public decimal ThisYearRevenue { get; set; }
        public decimal TotalOutstandingPayments { get; set; }
        public decimal ThisMonthExpenses { get; set; }
        public decimal NetProfitThisMonth { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal MonthlyRevenue { get; set; }

        // إحصائيات الجلسات
        public int TotalSessionsThisMonth { get; set; }
        public int CompletedSessionsThisMonth { get; set; }
        public int CancelledSessionsThisMonth { get; set; }
        public int CompletedSessions { get; set; }

        // إحصائيات الأطباء
        public int TotalDoctors { get; set; }
        public int ActiveDoctors { get; set; }
        public double AverageDoctorOccupancy { get; set; }

        // إحصائيات المخزون
        public int LowStockItems { get; set; }
        public int OutOfStockItems { get; set; }
        public int ExpiringItemsThisMonth { get; set; }

        // إحصائيات الموظفين
        public int TotalEmployees { get; set; }
        public int PresentEmployeesToday { get; set; }
        public int AbsentEmployeesToday { get; set; }
        public int EmployeesOnLeave { get; set; }

        // العملة المختارة
        public string SelectedCurrency { get; set; } = "YER"; // YER, SAR, USD

        // آخر تحديث
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// نموذج بيانات المخطط البياني
    /// </summary>
    public class ChartData
    {
        public string Label { get; set; } = string.Empty;
        public double Value { get; set; }
        public string Color { get; set; } = "#3498DB";
        public DateTime Date { get; set; }
    }

    /// <summary>
    /// نموذج إحصائيات الإيرادات الشهرية
    /// </summary>
    public class MonthlyRevenueData
    {
        public string Month { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public decimal Expenses { get; set; }
        public decimal NetProfit { get; set; }
        public int PatientCount { get; set; }
        public int SessionCount { get; set; }
    }

    /// <summary>
    /// نموذج توزيع أنواع العلاج
    /// </summary>
    public class TreatmentTypeDistribution
    {
        public string TreatmentType { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal TotalRevenue { get; set; }
        public double Percentage { get; set; }
        public string Color { get; set; } = "#3498DB";
    }

    /// <summary>
    /// نموذج إحصائيات الطبيب
    /// </summary>
    public class DoctorStatistics
    {
        public int DoctorId { get; set; }
        public string DoctorName { get; set; } = string.Empty;
        public string DoctorFullName { get; set; } = string.Empty;
        public int TotalAppointments { get; set; }
        public int CompletedAppointments { get; set; }
        public int CancelledAppointments { get; set; }
        public int SessionsCount { get; set; }
        public int CompletedSessions { get; set; }
        public decimal TotalRevenue { get; set; }
        public double CompletionRate { get; set; }
        public double OccupancyRate { get; set; }
        public int PatientCount { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج المواعيد القادمة
    /// </summary>
    public class UpcomingAppointment
    {
        public int AppointmentId { get; set; }
        public string PatientName { get; set; } = string.Empty;
        public string PatientPhone { get; set; } = string.Empty;
        public string DoctorName { get; set; } = string.Empty;
        public DateTime AppointmentDateTime { get; set; }
        public DateTime AppointmentTime { get; set; }
        public string TreatmentType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string PaymentStatus { get; set; } = string.Empty;
        public decimal Cost { get; set; }
        public bool IsToday { get; set; }
        public bool IsTomorrow { get; set; }
        public bool IsOverdue { get; set; }
    }

    /// <summary>
    /// نموذج التنبيهات الذكية
    /// </summary>
    public class SmartAlert
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string AlertType { get; set; } = string.Empty; // Payment, Inventory, Appointment, System
        public AlertType Type { get; set; } = Dashboard.AlertType.Info;
        public string Priority { get; set; } = "عادي"; // عادي، مهم، عاجل
        public string Icon { get; set; } = "⚠️";
        public string Color { get; set; } = "#F39C12";
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public bool IsRead { get; set; } = false;
        public string ActionUrl { get; set; } = string.Empty;
        public Dictionary<string, object> Data { get; set; } = new();
    }

    /// <summary>
    /// نموذج إعدادات الداشبورد
    /// </summary>
    public class DashboardSettings
    {
        public bool AutoRefresh { get; set; } = true;
        public int RefreshIntervalMinutes { get; set; } = 5;
        public string DefaultCurrency { get; set; } = "YER";
        public bool ShowFinancialData { get; set; } = true;
        public bool ShowPatientData { get; set; } = true;
        public bool ShowAppointmentData { get; set; } = true;
        public bool ShowInventoryAlerts { get; set; } = true;
        public bool ShowPaymentAlerts { get; set; } = true;
        public List<string> VisibleWidgets { get; set; } = new();
        public Dictionary<string, int> WidgetPositions { get; set; } = new();
    }

    /// <summary>
    /// نموذج بيانات الأداء اليومي
    /// </summary>
    public class DailyPerformanceData
    {
        public DateTime Date { get; set; }
        public int AppointmentsScheduled { get; set; }
        public int AppointmentsCompleted { get; set; }
        public int AppointmentsCancelled { get; set; }
        public int NewPatients { get; set; }
        public decimal Revenue { get; set; }
        public decimal Expenses { get; set; }
        public int SessionsCompleted { get; set; }
        public double CompletionRate { get; set; }
        public double PatientSatisfaction { get; set; }
    }

    /// <summary>
    /// نموذج إحصائيات المدفوعات
    /// </summary>
    public class PaymentStatistics
    {
        public decimal TotalReceived { get; set; }
        public decimal TotalOutstanding { get; set; }
        public decimal OverduePayments { get; set; }
        public int PaymentsReceivedToday { get; set; }
        public int OutstandingInvoices { get; set; }
        public int OverdueInvoices { get; set; }
        public Dictionary<string, decimal> PaymentsByMethod { get; set; } = new();
        public List<PatientDebt> TopDebtors { get; set; } = new();
    }

    /// <summary>
    /// نموذج ديون المرضى
    /// </summary>
    public class PatientDebt
    {
        public int PatientId { get; set; }
        public string PatientName { get; set; } = string.Empty;
        public string PatientPhone { get; set; } = string.Empty;
        public decimal TotalDebt { get; set; }
        public DateTime LastPaymentDate { get; set; }
        public int DaysOverdue { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج إحصائيات المخزون
    /// </summary>
    public class InventoryStatistics
    {
        public int TotalItems { get; set; }
        public int LowStockItems { get; set; }
        public int OutOfStockItems { get; set; }
        public int ExpiringItems { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public List<LowStockItem> LowStockItemsList { get; set; } = new();
        public List<ExpiringItem> ExpiringItemsList { get; set; } = new();
    }

    /// <summary>
    /// نموذج بيانات الأداء اليومي المحسن
    /// </summary>
    public class EnhancedDailyPerformanceData
    {
        public DateTime Date { get; set; }
        public int PatientsCount { get; set; }
        public int AppointmentsCount { get; set; }
        public int CompletedAppointments { get; set; }
        public int CancelledAppointments { get; set; }
        public int MissedAppointments { get; set; }
        public decimal Revenue { get; set; }
        public decimal Expenses { get; set; }
        public decimal NetProfit { get; set; }
        public int SessionsCount { get; set; }
        public int CompletedSessions { get; set; }
        public double OccupancyRate { get; set; }
        public string Currency { get; set; } = "YER";
    }

    /// <summary>
    /// نموذج إحصائيات الأطباء المحسن
    /// </summary>
    public class EnhancedDoctorStatistics
    {
        public int DoctorId { get; set; }
        public string DoctorName { get; set; } = string.Empty;
        public string DoctorFullName { get; set; } = string.Empty;
        public string Specialty { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public int TotalAppointments { get; set; }
        public int CompletedAppointments { get; set; }
        public int CancelledAppointments { get; set; }
        public int MissedAppointments { get; set; }
        public int SessionsCount { get; set; }
        public int CompletedSessions { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal MonthlyRevenue { get; set; }
        public double CompletionRate { get; set; }
        public double OccupancyRate { get; set; }
        public int PatientCount { get; set; }
        public int NewPatientsThisMonth { get; set; }
        public decimal AverageSessionValue { get; set; }
        public double PatientSatisfactionRate { get; set; }
        public List<MonthlyEarning> MonthlyEarnings { get; set; } = new();
        public string Currency { get; set; } = "YER";
    }

    /// <summary>
    /// نموذج الأرباح الشهرية للطبيب
    /// </summary>
    public class MonthlyEarning
    {
        public string Month { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public int SessionsCount { get; set; }
        public int PatientsCount { get; set; }
    }

    /// <summary>
    /// نموذج التنبيهات الذكية المحسن
    /// </summary>
    public class EnhancedSmartAlert
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public AlertType Type { get; set; }
        public AlertPriority Priority { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public bool IsRead { get; set; } = false;
        public bool IsActionRequired { get; set; } = false;
        public string ActionUrl { get; set; } = string.Empty;
        public string ActionText { get; set; } = string.Empty;
        public Dictionary<string, object> Data { get; set; } = new();
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = "#3498DB";
    }

    /// <summary>
    /// أولوية التنبيه
    /// </summary>
    public enum AlertPriority
    {
        Low = 1,
        Normal = 2,
        High = 3,
        Critical = 4
    }

    /// <summary>
    /// نموذج بيانات المخطط البياني المحسن
    /// </summary>
    public class EnhancedChartData
    {
        public string Label { get; set; } = string.Empty;
        public double Value { get; set; }
        public string Color { get; set; } = "#3498DB";
        public DateTime Date { get; set; }
        public string Category { get; set; } = string.Empty;
        public Dictionary<string, object> AdditionalData { get; set; } = new();
        public string FormattedValue { get; set; } = string.Empty;
        public string Tooltip { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج إحصائيات الإيرادات المحسن
    /// </summary>
    public class EnhancedRevenueStatistics
    {
        public decimal TodayRevenue { get; set; }
        public decimal YesterdayRevenue { get; set; }
        public decimal ThisWeekRevenue { get; set; }
        public decimal LastWeekRevenue { get; set; }
        public decimal ThisMonthRevenue { get; set; }
        public decimal LastMonthRevenue { get; set; }
        public decimal ThisYearRevenue { get; set; }
        public decimal LastYearRevenue { get; set; }
        public decimal AverageMonthlyRevenue { get; set; }
        public decimal ProjectedMonthlyRevenue { get; set; }
        public double GrowthRate { get; set; }
        public List<MonthlyRevenueData> MonthlyData { get; set; } = new();
        public List<DailyRevenueData> DailyData { get; set; } = new();
        public string Currency { get; set; } = "YER";
    }

    /// <summary>
    /// نموذج بيانات الإيرادات اليومية
    /// </summary>
    public class DailyRevenueData
    {
        public DateTime Date { get; set; }
        public decimal Revenue { get; set; }
        public decimal Expenses { get; set; }
        public decimal NetProfit { get; set; }
        public int TransactionsCount { get; set; }
        public int PatientsCount { get; set; }
    }

    /// <summary>
    /// نموذج المواد منخفضة المخزون
    /// </summary>
    public class LowStockItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public int MinimumStock { get; set; }
        public string Unit { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج المواد منتهية الصلاحية
    /// </summary>
    public class ExpiringItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public DateTime ExpiryDate { get; set; }
        public int DaysToExpiry { get; set; }
        public int Quantity { get; set; }
        public string BatchNumber { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج إعدادات العملة
    /// </summary>
    public class CurrencySettings
    {
        public string Code { get; set; } = "YER";
        public string Symbol { get; set; } = "ر.ي";
        public string Name { get; set; } = "ريال يمني";
        public decimal ExchangeRate { get; set; } = 1.0m;
        public bool IsDefault { get; set; } = true;
        public string Format { get; set; } = "{0:N0} {1}";
    }

    /// <summary>
    /// نموذج إحصائيات المواعيد المحسن
    /// </summary>
    public class EnhancedAppointmentStatistics
    {
        public int TodayAppointments { get; set; }
        public int TomorrowAppointments { get; set; }
        public int ThisWeekAppointments { get; set; }
        public int NextWeekAppointments { get; set; }
        public int ThisMonthAppointments { get; set; }
        public int CompletedAppointmentsToday { get; set; }
        public int CancelledAppointmentsToday { get; set; }
        public int MissedAppointmentsToday { get; set; }
        public int RescheduledAppointmentsToday { get; set; }
        public double CompletionRate { get; set; }
        public double CancellationRate { get; set; }
        public double NoShowRate { get; set; }
        public List<HourlyAppointmentData> HourlyDistribution { get; set; } = new();
        public List<DoctorAppointmentData> DoctorDistribution { get; set; } = new();
    }

    /// <summary>
    /// نموذج بيانات المواعيد بالساعة
    /// </summary>
    public class HourlyAppointmentData
    {
        public int Hour { get; set; }
        public int AppointmentsCount { get; set; }
        public int CompletedCount { get; set; }
        public int CancelledCount { get; set; }
        public string TimeSlot { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج بيانات مواعيد الطبيب
    /// </summary>
    public class DoctorAppointmentData
    {
        public int DoctorId { get; set; }
        public string DoctorName { get; set; } = string.Empty;
        public int AppointmentsCount { get; set; }
        public int CompletedCount { get; set; }
        public int CancelledCount { get; set; }
        public double OccupancyRate { get; set; }
    }

    /// <summary>
    /// نموذج إحصائيات المرضى المحسن
    /// </summary>
    public class EnhancedPatientStatistics
    {
        public int TotalPatients { get; set; }
        public int ActivePatients { get; set; }
        public int InactivePatients { get; set; }
        public int ArchivedPatients { get; set; }
        public int NewPatientsToday { get; set; }
        public int NewPatientsThisWeek { get; set; }
        public int NewPatientsThisMonth { get; set; }
        public int NewPatientsThisYear { get; set; }
        public int PatientsWithOutstandingPayments { get; set; }
        public int PatientsWithCompletedTreatment { get; set; }
        public int PatientsInTreatment { get; set; }
        public double PatientRetentionRate { get; set; }
        public double PatientSatisfactionRate { get; set; }
        public List<AgeGroupData> AgeDistribution { get; set; } = new();
        public List<GenderData> GenderDistribution { get; set; } = new();
        public List<TreatmentTypeData> TreatmentTypeDistribution { get; set; } = new();
    }

    /// <summary>
    /// نموذج بيانات الفئة العمرية
    /// </summary>
    public class AgeGroupData
    {
        public string AgeGroup { get; set; } = string.Empty;
        public int Count { get; set; }
        public double Percentage { get; set; }
    }

    /// <summary>
    /// نموذج بيانات الجنس
    /// </summary>
    public class GenderData
    {
        public string Gender { get; set; } = string.Empty;
        public int Count { get; set; }
        public double Percentage { get; set; }
    }

    /// <summary>
    /// نموذج بيانات نوع العلاج
    /// </summary>
    public class TreatmentTypeData
    {
        public string TreatmentType { get; set; } = string.Empty;
        public int Count { get; set; }
        public double Percentage { get; set; }
        public decimal Revenue { get; set; }
        public string Color { get; set; } = "#3498DB";
    }

    /// <summary>
    /// نموذج إحصائيات الموظفين المحسن
    /// </summary>
    public class EnhancedEmployeeStatistics
    {
        public int TotalEmployees { get; set; }
        public int ActiveEmployees { get; set; }
        public int InactiveEmployees { get; set; }
        public int PresentEmployeesToday { get; set; }
        public int AbsentEmployeesToday { get; set; }
        public int LateEmployeesToday { get; set; }
        public int EmployeesOnLeave { get; set; }
        public int EmployeesOnVacation { get; set; }
        public double AttendanceRate { get; set; }
        public double PunctualityRate { get; set; }
        public List<DepartmentData> DepartmentDistribution { get; set; } = new();
        public List<EmployeePerformanceData> TopPerformers { get; set; } = new();
    }

    /// <summary>
    /// نموذج بيانات القسم
    /// </summary>
    public class DepartmentData
    {
        public string Department { get; set; } = string.Empty;
        public int EmployeeCount { get; set; }
        public int PresentCount { get; set; }
        public int AbsentCount { get; set; }
        public double AttendanceRate { get; set; }
    }

    /// <summary>
    /// نموذج بيانات أداء الموظف
    /// </summary>
    public class EmployeePerformanceData
    {
        public int EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        public double PerformanceScore { get; set; }
        public int AttendanceDays { get; set; }
        public int AbsentDays { get; set; }
        public double AttendanceRate { get; set; }
    }

}
