using System.ComponentModel.DataAnnotations;

namespace AqlanCenterProApp.Models.Dashboard
{
    /// <summary>
    /// أنواع التنبيهات
    /// </summary>
    public enum AlertType
    {
        Info,
        Warning,
        Error,
        Success,
        Payment,
        Inventory,
        Appointment,
        System
    }

    /// <summary>
    /// نموذج إحصائيات الداشبورد الشاملة
    /// </summary>
    public class DashboardStatistics
    {
        // إحصائيات المرضى
        public int TotalPatients { get; set; }
        public int ActivePatients { get; set; }
        public int NewPatientsThisMonth { get; set; }
        public int NewPatientsToday { get; set; }
        public int ArchivedPatients { get; set; }
        public int PatientsWithOutstandingPayments { get; set; }

        // إحصائيات المواعيد
        public int TodayAppointments { get; set; }
        public int TomorrowAppointments { get; set; }
        public int ThisWeekAppointments { get; set; }
        public int CompletedAppointmentsToday { get; set; }
        public int CancelledAppointmentsToday { get; set; }
        public int MissedAppointmentsToday { get; set; }
        public int PendingAppointments { get; set; }
        public int CancelledAppointments { get; set; }

        // الإحصائيات المالية
        public decimal TodayRevenue { get; set; }
        public decimal ThisMonthRevenue { get; set; }
        public decimal ThisYearRevenue { get; set; }
        public decimal TotalOutstandingPayments { get; set; }
        public decimal ThisMonthExpenses { get; set; }
        public decimal NetProfitThisMonth { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal MonthlyRevenue { get; set; }

        // إحصائيات الجلسات
        public int TotalSessionsThisMonth { get; set; }
        public int CompletedSessionsThisMonth { get; set; }
        public int CancelledSessionsThisMonth { get; set; }
        public int CompletedSessions { get; set; }

        // إحصائيات الأطباء
        public int TotalDoctors { get; set; }
        public int ActiveDoctors { get; set; }
        public double AverageDoctorOccupancy { get; set; }

        // إحصائيات المخزون
        public int LowStockItems { get; set; }
        public int OutOfStockItems { get; set; }
        public int ExpiringItemsThisMonth { get; set; }

        // إحصائيات الموظفين
        public int TotalEmployees { get; set; }
        public int PresentEmployeesToday { get; set; }
        public int AbsentEmployeesToday { get; set; }
        public int EmployeesOnLeave { get; set; }

        // العملة المختارة
        public string SelectedCurrency { get; set; } = "YER"; // YER, SAR, USD

        // آخر تحديث
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// نموذج بيانات المخطط البياني
    /// </summary>
    public class ChartData
    {
        public string Label { get; set; } = string.Empty;
        public double Value { get; set; }
        public string Color { get; set; } = "#3498DB";
        public DateTime Date { get; set; }
    }

    /// <summary>
    /// نموذج إحصائيات الإيرادات الشهرية
    /// </summary>
    public class MonthlyRevenueData
    {
        public string Month { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public decimal Expenses { get; set; }
        public decimal NetProfit { get; set; }
        public int PatientCount { get; set; }
        public int SessionCount { get; set; }
    }

    /// <summary>
    /// نموذج توزيع أنواع العلاج
    /// </summary>
    public class TreatmentTypeDistribution
    {
        public string TreatmentType { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal TotalRevenue { get; set; }
        public double Percentage { get; set; }
        public string Color { get; set; } = "#3498DB";
    }

    /// <summary>
    /// نموذج إحصائيات الطبيب
    /// </summary>
    public class DoctorStatistics
    {
        public int DoctorId { get; set; }
        public string DoctorName { get; set; } = string.Empty;
        public string DoctorFullName { get; set; } = string.Empty;
        public int TotalAppointments { get; set; }
        public int CompletedAppointments { get; set; }
        public int CancelledAppointments { get; set; }
        public int SessionsCount { get; set; }
        public int CompletedSessions { get; set; }
        public decimal TotalRevenue { get; set; }
        public double CompletionRate { get; set; }
        public double OccupancyRate { get; set; }
        public int PatientCount { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج المواعيد القادمة
    /// </summary>
    public class UpcomingAppointment
    {
        public int AppointmentId { get; set; }
        public string PatientName { get; set; } = string.Empty;
        public string PatientPhone { get; set; } = string.Empty;
        public string DoctorName { get; set; } = string.Empty;
        public DateTime AppointmentDateTime { get; set; }
        public DateTime AppointmentTime { get; set; }
        public string TreatmentType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string PaymentStatus { get; set; } = string.Empty;
        public decimal Cost { get; set; }
        public bool IsToday { get; set; }
        public bool IsTomorrow { get; set; }
        public bool IsOverdue { get; set; }
    }

    /// <summary>
    /// نموذج التنبيهات الذكية
    /// </summary>
    public class SmartAlert
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string AlertType { get; set; } = string.Empty; // Payment, Inventory, Appointment, System
        public AlertType Type { get; set; } = Dashboard.AlertType.Info;
        public string Priority { get; set; } = "عادي"; // عادي، مهم، عاجل
        public string Icon { get; set; } = "⚠️";
        public string Color { get; set; } = "#F39C12";
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public bool IsRead { get; set; } = false;
        public string ActionUrl { get; set; } = string.Empty;
        public Dictionary<string, object> Data { get; set; } = new();
    }

    /// <summary>
    /// نموذج إعدادات الداشبورد
    /// </summary>
    public class DashboardSettings
    {
        public bool AutoRefresh { get; set; } = true;
        public int RefreshIntervalMinutes { get; set; } = 5;
        public string DefaultCurrency { get; set; } = "YER";
        public bool ShowFinancialData { get; set; } = true;
        public bool ShowPatientData { get; set; } = true;
        public bool ShowAppointmentData { get; set; } = true;
        public bool ShowInventoryAlerts { get; set; } = true;
        public bool ShowPaymentAlerts { get; set; } = true;
        public List<string> VisibleWidgets { get; set; } = new();
        public Dictionary<string, int> WidgetPositions { get; set; } = new();
    }

    /// <summary>
    /// نموذج بيانات الأداء اليومي
    /// </summary>
    public class DailyPerformanceData
    {
        public DateTime Date { get; set; }
        public int AppointmentsScheduled { get; set; }
        public int AppointmentsCompleted { get; set; }
        public int AppointmentsCancelled { get; set; }
        public int NewPatients { get; set; }
        public decimal Revenue { get; set; }
        public decimal Expenses { get; set; }
        public int SessionsCompleted { get; set; }
        public double CompletionRate { get; set; }
        public double PatientSatisfaction { get; set; }
    }

    /// <summary>
    /// نموذج إحصائيات المدفوعات
    /// </summary>
    public class PaymentStatistics
    {
        public decimal TotalReceived { get; set; }
        public decimal TotalOutstanding { get; set; }
        public decimal OverduePayments { get; set; }
        public int PaymentsReceivedToday { get; set; }
        public int OutstandingInvoices { get; set; }
        public int OverdueInvoices { get; set; }
        public Dictionary<string, decimal> PaymentsByMethod { get; set; } = new();
        public List<PatientDebt> TopDebtors { get; set; } = new();
    }

    /// <summary>
    /// نموذج ديون المرضى
    /// </summary>
    public class PatientDebt
    {
        public int PatientId { get; set; }
        public string PatientName { get; set; } = string.Empty;
        public string PatientPhone { get; set; } = string.Empty;
        public decimal TotalDebt { get; set; }
        public DateTime LastPaymentDate { get; set; }
        public int DaysOverdue { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج إحصائيات المخزون
    /// </summary>
    public class InventoryStatistics
    {
        public int TotalItems { get; set; }
        public int LowStockItems { get; set; }
        public int OutOfStockItems { get; set; }
        public int ExpiringItems { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public List<LowStockItem> LowStockItemsList { get; set; } = new();
        public List<ExpiringItem> ExpiringItemsList { get; set; } = new();
    }

    /// <summary>
    /// نموذج المواد منخفضة المخزون
    /// </summary>
    public class LowStockItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public int MinimumStock { get; set; }
        public string Unit { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج المواد منتهية الصلاحية
    /// </summary>
    public class ExpiringItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public DateTime ExpiryDate { get; set; }
        public int DaysToExpiry { get; set; }
        public int Quantity { get; set; }
        public string BatchNumber { get; set; } = string.Empty;
    }

}
