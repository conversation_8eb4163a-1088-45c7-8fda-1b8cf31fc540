using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;

namespace AqlanCenterProApp.Services.Implementations
{
    /// <summary>
    /// خدمة إدارة الأداء والكاش المتقدمة
    /// </summary>
    public class PerformanceService
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<PerformanceService> _logger;
        private readonly ConcurrentDictionary<string, PerformanceMetric> _performanceMetrics;
        private readonly object _lockObject = new object();

        private readonly TimeSpan _defaultCacheExpiration = TimeSpan.FromMinutes(5);
        private readonly int _maxCacheSize = 1000;
        private int _currentCacheSize = 0;

        public PerformanceService(
            IMemoryCache cache,
            ILogger<PerformanceService> logger)
        {
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _performanceMetrics = new ConcurrentDictionary<string, PerformanceMetric>();
        }

        /// <summary>
        /// تنفيذ عملية مع قياس الأداء والكاش
        /// </summary>
        public async Task<T> ExecuteWithCacheAsync<T>(
            string cacheKey,
            Func<Task<T>> operation,
            TimeSpan? expiration = null,
            bool forceRefresh = false)
        {
            var stopwatch = Stopwatch.StartNew();
            var operationName = cacheKey.Split('_')[0];

            try
            {
                // التحقق من الكاش أولاً
                if (!forceRefresh && _cache.TryGetValue(cacheKey, out T cachedResult))
                {
                    stopwatch.Stop();
                    RecordPerformanceMetric(operationName, stopwatch.Elapsed, true);
                    _logger.LogDebug($"تم استرجاع البيانات من الكاش: {cacheKey} في {stopwatch.ElapsedMilliseconds} مللي ثانية");
                    return cachedResult;
                }

                // تنفيذ العملية
                var result = await operation();
                stopwatch.Stop();

                // حفظ في الكاش
                var cacheExpiration = expiration ?? _defaultCacheExpiration;
                await SetCacheAsync(cacheKey, result, cacheExpiration);

                RecordPerformanceMetric(operationName, stopwatch.Elapsed, false);
                _logger.LogDebug($"تم تنفيذ العملية: {operationName} في {stopwatch.ElapsedMilliseconds} مللي ثانية");

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                RecordPerformanceMetric(operationName, stopwatch.Elapsed, false, true);
                _logger.LogError(ex, $"خطأ في تنفيذ العملية: {operationName}");
                throw;
            }
        }

        /// <summary>
        /// حفظ البيانات في الكاش مع إدارة الحجم
        /// </summary>
        private async Task SetCacheAsync<T>(string key, T value, TimeSpan expiration)
        {
            await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    // التحقق من حجم الكاش
                    if (_currentCacheSize >= _maxCacheSize)
                    {
                        CleanupOldestCacheEntries();
                    }

                    var options = new MemoryCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = expiration,
                        Priority = CacheItemPriority.Normal,
                        PostEvictionCallbacks = { new PostEvictionCallbackRegistration
                        {
                            EvictionCallback = OnCacheEntryEvicted
                        }}
                    };

                    _cache.Set(key, value, options);
                    _currentCacheSize++;
                }
            });
        }

        /// <summary>
        /// تنظيف أقدم عناصر الكاش
        /// </summary>
        private void CleanupOldestCacheEntries()
        {
            try
            {
                // إزالة 20% من الكاش
                var entriesToRemove = (int)(_maxCacheSize * 0.2);
                
                // هنا يمكن تنفيذ منطق أكثر تعقيداً لإزالة العناصر الأقل استخداماً
                // حالياً سنقوم بتقليل العداد فقط
                _currentCacheSize = Math.Max(0, _currentCacheSize - entriesToRemove);
                
                _logger.LogDebug($"تم تنظيف {entriesToRemove} عنصر من الكاش");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "خطأ في تنظيف الكاش");
            }
        }

        /// <summary>
        /// معالج إزالة عنصر من الكاش
        /// </summary>
        private void OnCacheEntryEvicted(object key, object value, EvictionReason reason, object state)
        {
            lock (_lockObject)
            {
                _currentCacheSize = Math.Max(0, _currentCacheSize - 1);
            }
            
            _logger.LogDebug($"تم إزالة عنصر من الكاش: {key}, السبب: {reason}");
        }

        /// <summary>
        /// تسجيل مقياس الأداء
        /// </summary>
        private void RecordPerformanceMetric(string operationName, TimeSpan duration, bool fromCache, bool hasError = false)
        {
            _performanceMetrics.AddOrUpdate(operationName, 
                new PerformanceMetric
                {
                    OperationName = operationName,
                    TotalExecutions = 1,
                    TotalDuration = duration,
                    CacheHits = fromCache ? 1 : 0,
                    Errors = hasError ? 1 : 0,
                    LastExecution = DateTime.Now,
                    MinDuration = duration,
                    MaxDuration = duration
                },
                (key, existing) =>
                {
                    existing.TotalExecutions++;
                    existing.TotalDuration = existing.TotalDuration.Add(duration);
                    existing.CacheHits += fromCache ? 1 : 0;
                    existing.Errors += hasError ? 1 : 0;
                    existing.LastExecution = DateTime.Now;
                    existing.MinDuration = duration < existing.MinDuration ? duration : existing.MinDuration;
                    existing.MaxDuration = duration > existing.MaxDuration ? duration : existing.MaxDuration;
                    return existing;
                });
        }

        /// <summary>
        /// الحصول على إحصائيات الأداء
        /// </summary>
        public Dictionary<string, object> GetPerformanceStatistics()
        {
            var stats = new Dictionary<string, object>();

            try
            {
                var totalOperations = _performanceMetrics.Values.Sum(m => m.TotalExecutions);
                var totalCacheHits = _performanceMetrics.Values.Sum(m => m.CacheHits);
                var totalErrors = _performanceMetrics.Values.Sum(m => m.Errors);
                var averageDuration = _performanceMetrics.Values.Any() 
                    ? TimeSpan.FromTicks(_performanceMetrics.Values.Sum(m => m.TotalDuration.Ticks) / _performanceMetrics.Values.Sum(m => m.TotalExecutions))
                    : TimeSpan.Zero;

                stats["TotalOperations"] = totalOperations;
                stats["CacheHitRate"] = totalOperations > 0 ? (double)totalCacheHits / totalOperations * 100 : 0;
                stats["ErrorRate"] = totalOperations > 0 ? (double)totalErrors / totalOperations * 100 : 0;
                stats["AverageDuration"] = averageDuration.TotalMilliseconds;
                stats["CacheSize"] = _currentCacheSize;
                stats["MaxCacheSize"] = _maxCacheSize;
                stats["CacheUtilization"] = _maxCacheSize > 0 ? (double)_currentCacheSize / _maxCacheSize * 100 : 0;

                // إحصائيات مفصلة لكل عملية
                var operationStats = new Dictionary<string, object>();
                foreach (var metric in _performanceMetrics.Values.OrderByDescending(m => m.TotalExecutions).Take(10))
                {
                    operationStats[metric.OperationName] = new
                    {
                        TotalExecutions = metric.TotalExecutions,
                        AverageDuration = metric.TotalExecutions > 0 ? metric.TotalDuration.TotalMilliseconds / metric.TotalExecutions : 0,
                        MinDuration = metric.MinDuration.TotalMilliseconds,
                        MaxDuration = metric.MaxDuration.TotalMilliseconds,
                        CacheHitRate = metric.TotalExecutions > 0 ? (double)metric.CacheHits / metric.TotalExecutions * 100 : 0,
                        ErrorRate = metric.TotalExecutions > 0 ? (double)metric.Errors / metric.TotalExecutions * 100 : 0,
                        LastExecution = metric.LastExecution
                    };
                }
                stats["OperationDetails"] = operationStats;

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على إحصائيات الأداء");
                return new Dictionary<string, object> { ["Error"] = ex.Message };
            }
        }

        /// <summary>
        /// تحسين الأداء وتنظيف الكاش
        /// </summary>
        public async Task OptimizePerformanceAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    lock (_lockObject)
                    {
                        // تنظيف الكاش
                        CleanupOldestCacheEntries();

                        // إعادة تعيين الإحصائيات القديمة
                        var oldMetrics = _performanceMetrics.Values
                            .Where(m => DateTime.Now - m.LastExecution > TimeSpan.FromHours(24))
                            .Select(m => m.OperationName)
                            .ToList();

                        foreach (var operationName in oldMetrics)
                        {
                            _performanceMetrics.TryRemove(operationName, out _);
                        }

                        // تشغيل garbage collection
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                        GC.Collect();
                    }

                    _logger.LogInformation("تم تحسين الأداء وتنظيف الكاش بنجاح");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "خطأ في تحسين الأداء");
                }
            });
        }

        /// <summary>
        /// مسح الكاش بالكامل
        /// </summary>
        public void ClearCache()
        {
            try
            {
                lock (_lockObject)
                {
                    if (_cache is MemoryCache memoryCache)
                    {
                        memoryCache.Compact(1.0); // إزالة جميع العناصر
                    }
                    _currentCacheSize = 0;
                }

                _logger.LogInformation("تم مسح الكاش بالكامل");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في مسح الكاش");
            }
        }

        /// <summary>
        /// إزالة عنصر محدد من الكاش
        /// </summary>
        public void RemoveFromCache(string key)
        {
            try
            {
                _cache.Remove(key);
                _logger.LogDebug($"تم إزالة العنصر من الكاش: {key}");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"خطأ في إزالة العنصر من الكاش: {key}");
            }
        }
    }

    /// <summary>
    /// مقياس الأداء للعملية
    /// </summary>
    public class PerformanceMetric
    {
        public string OperationName { get; set; }
        public int TotalExecutions { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public int CacheHits { get; set; }
        public int Errors { get; set; }
        public DateTime LastExecution { get; set; }
        public TimeSpan MinDuration { get; set; }
        public TimeSpan MaxDuration { get; set; }
    }
}
