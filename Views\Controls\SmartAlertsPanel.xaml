<UserControl x:Class="AqlanCenterProApp.Views.Controls.SmartAlertsPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:controls="clr-namespace:AqlanCenterProApp.Views.Controls"
             mc:Ignorable="d"
             d:DesignHeight="400"
             d:DesignWidth="600"
             FlowDirection="RightToLeft">

    <Border Style="{StaticResource ModernCardStyle}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- رأس التنبيهات -->
            <Grid Grid.Row="0" Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🔔" FontSize="20" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBlock Text="التنبيهات الذكية" 
                               Style="{StaticResource SectionHeaderStyle}"
                               VerticalAlignment="Center"/>
                    <Border Background="#E74C3C" 
                            CornerRadius="10" 
                            Padding="6,2" 
                            Margin="8,0,0,0"
                            Visibility="{Binding HasUnreadAlerts, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Text="{Binding UnreadAlertsCount}" 
                                   FontSize="11" 
                                   Foreground="White" 
                                   FontWeight="Bold"/>
                    </Border>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <!-- فلتر التنبيهات -->
                    <ComboBox ItemsSource="{Binding AlertTypeFilters}"
                              SelectedItem="{Binding SelectedAlertTypeFilter}"
                              Width="120"
                              Height="28"
                              FontSize="11"
                              Margin="0,0,8,0"/>

                    <!-- زر تحديث التنبيهات -->
                    <Button Content="🔄" 
                            Command="{Binding RefreshAlertsCommand}"
                            Width="28" 
                            Height="28"
                            Background="Transparent"
                            BorderBrush="#E0E0E0"
                            BorderThickness="1"
                            CornerRadius="6"
                            ToolTip="تحديث التنبيهات">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#F5F5F5"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- زر إعدادات التنبيهات -->
                    <Button Content="⚙" 
                            Command="{Binding AlertSettingsCommand}"
                            Width="28" 
                            Height="28"
                            Background="Transparent"
                            BorderBrush="#E0E0E0"
                            BorderThickness="1"
                            CornerRadius="6"
                            Margin="4,0,0,0"
                            ToolTip="إعدادات التنبيهات">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#F5F5F5"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>

            <!-- إحصائيات سريعة -->
            <UniformGrid Grid.Row="1" Columns="4" Margin="0,0,0,16">
                <!-- تنبيهات حرجة -->
                <Border Background="#FFEBEE" BorderBrush="#F44336" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="0,0,4,0">
                    <StackPanel>
                        <TextBlock Text="{Binding CriticalAlertsCount}" FontSize="18" FontWeight="Bold" Foreground="#D32F2F" HorizontalAlignment="Center"/>
                        <TextBlock Text="حرجة" FontSize="10" Foreground="#D32F2F" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- تنبيهات مهمة -->
                <Border Background="#FFF3E0" BorderBrush="#FF9800" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="2,0">
                    <StackPanel>
                        <TextBlock Text="{Binding HighPriorityAlertsCount}" FontSize="18" FontWeight="Bold" Foreground="#F57C00" HorizontalAlignment="Center"/>
                        <TextBlock Text="مهمة" FontSize="10" Foreground="#F57C00" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- تنبيهات عادية -->
                <Border Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="2,0">
                    <StackPanel>
                        <TextBlock Text="{Binding NormalAlertsCount}" FontSize="18" FontWeight="Bold" Foreground="#1976D2" HorizontalAlignment="Center"/>
                        <TextBlock Text="عادية" FontSize="10" Foreground="#1976D2" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- تنبيهات منخفضة -->
                <Border Background="#F1F8E9" BorderBrush="#4CAF50" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="4,0,0,0">
                    <StackPanel>
                        <TextBlock Text="{Binding LowPriorityAlertsCount}" FontSize="18" FontWeight="Bold" Foreground="#388E3C" HorizontalAlignment="Center"/>
                        <TextBlock Text="منخفضة" FontSize="10" Foreground="#388E3C" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </UniformGrid>

            <!-- قائمة التنبيهات -->
            <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                <ItemsControl ItemsSource="{Binding FilteredAlerts}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <controls:SmartAlertCard AlertTitle="{Binding Title}"
                                                     AlertMessage="{Binding Message}"
                                                     AlertIcon="{Binding Icon}"
                                                     AlertTime="{Binding CreatedAt, StringFormat='{}{0:HH:mm}'}"
                                                     ShowAction="{Binding IsActionRequired}"
                                                     ActionText="{Binding ActionText}"
                                                     ActionCommand="{Binding DataContext.ExecuteAlertActionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                     CloseCommand="{Binding DataContext.DismissAlertCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                     Margin="0,0,0,8"/>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>

            <!-- رسالة عدم وجود تنبيهات -->
            <StackPanel Grid.Row="2" 
                        VerticalAlignment="Center" 
                        HorizontalAlignment="Center"
                        Visibility="{Binding HasNoAlerts, Converter={StaticResource BooleanToVisibilityConverter}}">
                <TextBlock Text="✅" FontSize="48" HorizontalAlignment="Center" Foreground="#4CAF50" Margin="0,0,0,12"/>
                <TextBlock Text="لا توجد تنبيهات جديدة" FontSize="16" FontWeight="SemiBold" Foreground="#4CAF50" HorizontalAlignment="Center"/>
                <TextBlock Text="جميع الأمور تسير بشكل طبيعي" FontSize="12" Foreground="#81C784" HorizontalAlignment="Center" Margin="0,4,0,0"/>
            </StackPanel>

            <!-- مؤشر التحميل -->
            <Grid Grid.Row="2" 
                  Visibility="{Binding IsLoadingAlerts, Converter={StaticResource BooleanToVisibilityConverter}}"
                  Background="#80FFFFFF">
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                    <ProgressBar IsIndeterminate="True" Width="100" Height="4" Margin="0,0,0,12"/>
                    <TextBlock Text="جاري تحميل التنبيهات..." FontSize="14" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>

            <!-- تذييل التنبيهات -->
            <Grid Grid.Row="3" Margin="0,16,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="{Binding TotalAlertsCount, StringFormat='إجمالي التنبيهات: {0}'}" 
                               FontSize="11" 
                               Foreground="#95A5A6"/>
                    <TextBlock Text=" • " FontSize="11" Foreground="#95A5A6" Margin="8,0"/>
                    <TextBlock Text="{Binding LastAlertsUpdate, StringFormat='آخر تحديث: {0:HH:mm}'}" 
                               FontSize="11" 
                               Foreground="#95A5A6"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="وضع علامة مقروء على الكل" 
                            Command="{Binding MarkAllAsReadCommand}"
                            FontSize="11" 
                            Padding="8,4" 
                            Background="Transparent" 
                            Foreground="#3498DB" 
                            BorderThickness="0"
                            Cursor="Hand"
                            Margin="0,0,8,0"/>
                    
                    <Button Content="مسح الكل" 
                            Command="{Binding ClearAllAlertsCommand}"
                            FontSize="11" 
                            Padding="8,4" 
                            Background="Transparent" 
                            Foreground="#E74C3C" 
                            BorderThickness="0"
                            Cursor="Hand"/>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</UserControl>
