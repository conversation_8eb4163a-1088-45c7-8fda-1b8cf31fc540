using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace AqlanCenterProApp.Views.Controls
{
    /// <summary>
    /// Interaction logic for StatisticCard.xaml
    /// </summary>
    public partial class StatisticCard : UserControl
    {
        public StatisticCard()
        {
            InitializeComponent();
        }

        // خصائص البطاقة
        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register("Title", typeof(string), typeof(StatisticCard), new PropertyMetadata(string.Empty));

        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }

        public static readonly DependencyProperty ValueProperty =
            DependencyProperty.Register("Value", typeof(string), typeof(StatisticCard), new PropertyMetadata(string.Empty));

        public string Value
        {
            get { return (string)GetValue(ValueProperty); }
            set { SetValue(ValueProperty, value); }
        }

        public static readonly DependencyProperty IconProperty =
            DependencyProperty.Register("Icon", typeof(string), typeof(StatisticCard), new PropertyMetadata("📊"));

        public string Icon
        {
            get { return (string)GetValue(IconProperty); }
            set { SetValue(IconProperty, value); }
        }

        public static readonly DependencyProperty SubTextProperty =
            DependencyProperty.Register("SubText", typeof(string), typeof(StatisticCard), new PropertyMetadata(string.Empty));

        public string SubText
        {
            get { return (string)GetValue(SubTextProperty); }
            set { SetValue(SubTextProperty, value); }
        }

        public static readonly DependencyProperty CardBackgroundProperty =
            DependencyProperty.Register("CardBackground", typeof(Brush), typeof(StatisticCard), 
                new PropertyMetadata(new SolidColorBrush(Colors.White)));

        public Brush CardBackground
        {
            get { return (Brush)GetValue(CardBackgroundProperty); }
            set { SetValue(CardBackgroundProperty, value); }
        }

        public static readonly DependencyProperty IconBackgroundProperty =
            DependencyProperty.Register("IconBackground", typeof(Brush), typeof(StatisticCard), 
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(52, 152, 219))));

        public Brush IconBackground
        {
            get { return (Brush)GetValue(IconBackgroundProperty); }
            set { SetValue(IconBackgroundProperty, value); }
        }

        public static readonly DependencyProperty ValueColorProperty =
            DependencyProperty.Register("ValueColor", typeof(Brush), typeof(StatisticCard), 
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(44, 62, 80))));

        public Brush ValueColor
        {
            get { return (Brush)GetValue(ValueColorProperty); }
            set { SetValue(ValueColorProperty, value); }
        }

        // خصائص مؤشر التغيير
        public static readonly DependencyProperty ShowTrendProperty =
            DependencyProperty.Register("ShowTrend", typeof(bool), typeof(StatisticCard), new PropertyMetadata(false));

        public bool ShowTrend
        {
            get { return (bool)GetValue(ShowTrendProperty); }
            set { SetValue(ShowTrendProperty, value); }
        }

        public static readonly DependencyProperty TrendValueProperty =
            DependencyProperty.Register("TrendValue", typeof(string), typeof(StatisticCard), new PropertyMetadata(string.Empty));

        public string TrendValue
        {
            get { return (string)GetValue(TrendValueProperty); }
            set { SetValue(TrendValueProperty, value); }
        }

        public static readonly DependencyProperty TrendIconProperty =
            DependencyProperty.Register("TrendIcon", typeof(string), typeof(StatisticCard), new PropertyMetadata("↗"));

        public string TrendIcon
        {
            get { return (string)GetValue(TrendIconProperty); }
            set { SetValue(TrendIconProperty, value); }
        }

        public static readonly DependencyProperty TrendColorProperty =
            DependencyProperty.Register("TrendColor", typeof(Brush), typeof(StatisticCard), 
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(46, 204, 113))));

        public Brush TrendColor
        {
            get { return (Brush)GetValue(TrendColorProperty); }
            set { SetValue(TrendColorProperty, value); }
        }

        // طرق مساعدة لتعيين الألوان المحددة مسبقاً
        public void SetSuccessStyle()
        {
            CardBackground = new SolidColorBrush(Color.FromRgb(232, 245, 233));
            IconBackground = new SolidColorBrush(Color.FromRgb(76, 175, 80));
            ValueColor = new SolidColorBrush(Color.FromRgb(56, 142, 60));
        }

        public void SetWarningStyle()
        {
            CardBackground = new SolidColorBrush(Color.FromRgb(255, 243, 224));
            IconBackground = new SolidColorBrush(Color.FromRgb(255, 152, 0));
            ValueColor = new SolidColorBrush(Color.FromRgb(245, 124, 0));
        }

        public void SetDangerStyle()
        {
            CardBackground = new SolidColorBrush(Color.FromRgb(255, 235, 238));
            IconBackground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
            ValueColor = new SolidColorBrush(Color.FromRgb(211, 47, 47));
        }

        public void SetInfoStyle()
        {
            CardBackground = new SolidColorBrush(Color.FromRgb(227, 242, 253));
            IconBackground = new SolidColorBrush(Color.FromRgb(33, 150, 243));
            ValueColor = new SolidColorBrush(Color.FromRgb(25, 118, 210));
        }

        public void SetPrimaryStyle()
        {
            CardBackground = new SolidColorBrush(Color.FromRgb(232, 234, 246));
            IconBackground = new SolidColorBrush(Color.FromRgb(103, 58, 183));
            ValueColor = new SolidColorBrush(Color.FromRgb(81, 45, 168));
        }

        public void SetTrendUp(string value)
        {
            ShowTrend = true;
            TrendValue = value;
            TrendIcon = "↗";
            TrendColor = new SolidColorBrush(Color.FromRgb(46, 204, 113));
        }

        public void SetTrendDown(string value)
        {
            ShowTrend = true;
            TrendValue = value;
            TrendIcon = "↘";
            TrendColor = new SolidColorBrush(Color.FromRgb(231, 76, 60));
        }

        public void SetTrendFlat(string value)
        {
            ShowTrend = true;
            TrendValue = value;
            TrendIcon = "→";
            TrendColor = new SolidColorBrush(Color.FromRgb(149, 165, 166));
        }
    }
}
